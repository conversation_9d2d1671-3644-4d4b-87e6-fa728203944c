<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title th:text="${categorie.nomCategorie} + ' - TechShop'">Catégorie - TechShop</title>
</head>
<body>
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Accueil</a></li>
                <li class="breadcrumb-item"><a th:href="@{/categories}">Catégories</a></li>
                <li class="breadcrumb-item active" aria-current="page" th:text="${categorie.nomCategorie}">Catégorie</li>
            </ol>
        </nav>

        <!-- En-tête de la catégorie -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="bg-primary text-white p-4 rounded">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-2">
                                <i class="fas fa-tag"></i> <span th:text="${categorie.nomCategorie}">Nom de la catégorie</span>
                            </h1>
                            <p class="mb-2 lead" th:if="${categorie.typeCategorie}">
                                Type : <span th:text="${categorie.typeCategorie}">Type</span>
                            </p>
                            <div class="mb-0">
                                <small class="text-light" th:switch="${categorie.typeCategorie}">
                                    <span th:case="'CATEGORIE'">
                                        <i class="fas fa-info-circle"></i> Produits associés directement à cette catégorie
                                    </span>
                                    <span th:case="'SOUS_RAYON'">
                                        <i class="fas fa-info-circle"></i> Produits de toutes les catégories de ce sous-rayon
                                    </span>
                                    <span th:case="'RAYON'">
                                        <i class="fas fa-info-circle"></i> Produits de tous les sous-rayons et catégories de ce rayon
                                    </span>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="bg-white text-primary p-3 rounded">
                                <h3 class="mb-0" th:text="${#lists.size(produits)}">0</h3>
                                <small>Produit(s) disponible(s)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres et tri -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h4>Produits de cette catégorie</h4>
            </div>
            <div class="col-md-6">
                <div class="d-flex justify-content-end">
                    <select class="form-select" style="width: auto;" onchange="trierProduits(this.value)">
                        <option value="nom">Trier par nom</option>
                        <option value="prix-asc">Prix croissant</option>
                        <option value="prix-desc">Prix décroissant</option>
                        <option value="recent">Plus récents</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Grille des produits -->
        <div class="row" th:if="${produits != null and !#lists.isEmpty(produits)}">
            <div class="col-md-6 col-lg-4 mb-4" th:each="produit : ${produits}">
                <div class="card h-100 product-card">
                    <img th:src="${produit.urlImage} ?: 'https://via.placeholder.com/300x200'"
                         class="card-img-top" alt="Image produit" style="height: 200px; object-fit: cover;">

                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title" th:text="${produit.designation}">Nom du produit</h6>
                        <p class="card-text text-muted small" th:text="${produit.marque}">Marque</p>
                        <p class="card-text text-primary fw-bold fs-5 mt-auto"
                           th:text="${#numbers.formatDecimal(produit.prix, 0, 2)} + ' €'">Prix</p>

                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted" th:classappend="${produit.qteStock == 0 ? 'text-danger' : ''}">
                                <span th:if="${produit.qteStock > 0}">
                                    <i class="fas fa-check-circle text-success"></i> En stock
                                </span>
                                <span th:if="${produit.qteStock == 0}">
                                    <i class="fas fa-times-circle text-danger"></i> Rupture
                                </span>
                            </small>
                            <a th:href="@{/produits/{id}(id=${produit.id})}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> Détails
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message si aucun produit -->
        <div th:if="${produits == null or #lists.isEmpty(produits)}" class="text-center py-5">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">Aucun produit dans cette catégorie</h4>
            <p class="text-muted">Les produits seront bientôt disponibles.</p>
            <a th:href="@{/categories}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Retour aux catégories
            </a>
        </div>

        <!-- Autres catégories -->
        <div th:if="${categories != null and #lists.size(categories) > 1}" class="row mt-5">
            <div class="col-12">
                <h4 class="mb-4"><i class="fas fa-tags"></i> Autres Catégories</h4>
                <div class="row">
                    <div class="col-md-3 mb-3" th:each="cat : ${categories}" th:if="${cat.id != categorie.id}">
                        <a th:href="@{/categories/{id}(id=${cat.id})}" class="text-decoration-none">
                            <div class="card bg-light border-0 h-100 other-category">
                                <div class="card-body text-center">
                                    <i class="fas fa-tag fa-2x text-secondary mb-2"></i>
                                    <h6 class="card-title" th:text="${cat.nomCategorie}">Catégorie</h6>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="text-center mt-5">
            <a th:href="@{/categories}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left"></i> Toutes les catégories
            </a>
            <a th:href="@{/produits}" class="btn btn-primary">
                <i class="fas fa-shopping-bag"></i> Tous les produits
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <style>
        .product-card {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
        .other-category {
            transition: transform 0.3s;
        }
        .other-category:hover {
            transform: translateY(-2px);
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/techshop.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

    <script>
        // Debugging Bootstrap Dropdowns pour la page categories detail
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS (PAGE CATEGORIES DETAIL) ===');

            // Attendre que Bootstrap soit chargé
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap is loaded on categories detail page, version:', bootstrap.Tooltip.VERSION || 'unknown');

                    // Find all dropdown toggles
                    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                    console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles on categories detail page');

                    // Initialize dropdowns manually
                    dropdownToggles.forEach((toggle, index) => {
                        console.log(`🎯 Categories Detail Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                        try {
                            // Create Bootstrap dropdown instance
                            const dropdown = new bootstrap.Dropdown(toggle);
                            console.log(`✅ Categories Detail Dropdown ${index + 1} initialized successfully`);

                            // Add event listeners for debugging
                            toggle.addEventListener('show.bs.dropdown', function() {
                                console.log(`📂 Categories Detail Dropdown ${index + 1} is opening`);
                            });

                            toggle.addEventListener('shown.bs.dropdown', function() {
                                console.log(`📂 Categories Detail Dropdown ${index + 1} is now open`);
                            });

                            toggle.addEventListener('click', function(e) {
                                console.log(`🖱️ Categories Detail Dropdown ${index + 1} clicked`, e);
                            });

                        } catch (error) {
                            console.error(`❌ Error initializing categories detail dropdown ${index + 1}:`, error);
                        }
                    });

                } else {
                    console.error('❌ Bootstrap is not loaded on categories detail page!');
                }
            }, 500); // Attendre 500ms pour que Bootstrap soit chargé
        });

        function trierProduits(critere) {
            // Pour l'instant, on recharge la page avec le paramètre de tri
            // Dans une version plus avancée, on pourrait utiliser AJAX
            const url = new URL(window.location);
            url.searchParams.set('sort', critere);
            window.location.href = url.toString();
        }
    </script>
</body>
</html>
