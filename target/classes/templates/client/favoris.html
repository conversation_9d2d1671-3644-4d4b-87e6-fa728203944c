<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Mes Favoris - TechShop</title>
</head>
<body class="client-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <div class="client-main">
        <div class="container mt-4">
            <div class="row">
                <!-- Sidebar Client -->
                <div th:replace="~{fragments/layout :: client-sidebar}"></div>

            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a th:href="@{/}">Accueil</a></li>
                        <li class="breadcrumb-item"><a th:href="@{/client/dashboard}">Mon Compte</a></li>
                        <li class="breadcrumb-item active">Mes Favoris</li>
                    </ol>
                </nav>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-heart text-danger"></i> Mes Favoris
                        <span class="badge bg-secondary" th:text="${#lists.size(favoris)}">0</span>
                    </h2>
                    <div>
                        <a th:href="@{/produits}" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> Découvrir des produits
                        </a>
                    </div>
                </div>

                <!-- Messages -->
                <div class="alert alert-success alert-dismissible fade show" th:if="${success}">
                    <i class="fas fa-check-circle"></i> <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <div class="alert alert-danger alert-dismissible fade show" th:if="${error}">
                    <i class="fas fa-exclamation-triangle"></i> <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Liste des favoris -->
                <div th:if="${favoris != null and !#lists.isEmpty(favoris)}">
                    <div class="row">
                        <div class="col-md-6 col-lg-4 mb-4" th:each="favori : ${favoris}">
                            <div class="card h-100">
                                <div class="position-relative">
                                    <img th:src="${favori.produit.urlImage} ?: 'https://via.placeholder.com/300x200'"
                                         class="card-img-top" alt="Produit" style="height: 200px; object-fit: cover;">

                                    <!-- Badge stock -->
                                    <span class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success status-badge" th:if="${favori.produit.qteStock > 0}">
                                            <i class="fas fa-check"></i> En stock
                                        </span>
                                        <span class="badge bg-danger status-badge" th:if="${favori.produit.qteStock == 0}">
                                            <i class="fas fa-times"></i> Rupture
                                        </span>
                                    </span>
                                </div>

                                <div class="card-body">
                                    <h6 class="card-title" th:text="${favori.produit.designation}">Nom Produit</h6>
                                    <p class="card-text">
                                        <small class="text-muted" th:text="${favori.produit.marque}">Marque</small><br>
                                        <span class="badge bg-info status-badge" th:text="${favori.produit.categorie?.nomCategorie}">Catégorie</span>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="h5 text-primary mb-0" th:text="${#numbers.formatDecimal(favori.produit.prix, 0, 2)} + ' €'">Prix</span>
                                        <small class="text-muted" th:text="'Ajouté le ' + ${#temporals.format(favori.dateAjout, 'dd/MM/yyyy')}">Date</small>
                                    </div>
                                </div>

                                <div class="card-footer">
                                    <div class="d-flex justify-content-between">
                                        <a th:href="@{/produits/{id}(id=${favori.produit.id})}" class="btn btn-outline-primary btn-sm" title="Voir le produit">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-primary btn-sm"
                                                th:disabled="${favori.produit.qteStock == 0}"
                                                onclick="ajouterAuPanier([[${favori.produit.id}]], 1)">
                                            <i class="fas fa-cart-plus"></i> Panier
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm"
                                                onclick="retirerDesFavoris([[${favori.produit.id}]])">
                                            <i class="fas fa-heart-broken"></i> Retirer
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions groupées -->
                    <div class="card mt-4">
                        <div class="card-body">
                            <h5>Actions rapides</h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="ajouterTousAuPanier()">
                                    <i class="fas fa-cart-plus"></i> Ajouter tous au panier
                                </button>
                                <button class="btn btn-outline-danger" onclick="viderFavoris()">
                                    <i class="fas fa-trash"></i> Vider les favoris
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- État vide -->
                <div th:if="${favoris == null or #lists.isEmpty(favoris)}" class="text-center py-5">
                    <i class="fas fa-heart fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">Aucun produit en favoris</h4>
                    <p class="text-muted">Découvrez nos produits et ajoutez vos coups de cœur à vos favoris !</p>
                    <a th:href="@{/produits}" class="btn btn-primary">
                        <i class="fas fa-search"></i> Découvrir nos produits
                    </a>
                </div>
            </div>
            </div> <!-- End row -->
        </div> <!-- End container -->
    </div> <!-- End client-main -->

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <script>
        function ajouterAuPanier(produitId, quantite) {
            fetch('/client/panier/ajouter', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'produitId=' + produitId + '&quantite=' + quantite
            })
            .then(response => response.text())
            .then(data => {
                if (data === 'success') {
                    TechShop.showNotification('Produit ajouté au panier !', 'success');
                } else {
                    TechShop.showNotification('Erreur: ' + data, 'error');
                }
            })
            .catch(error => {
                TechShop.showNotification('Erreur de connexion', 'error');
            });
        }

        function retirerDesFavoris(produitId) {
            if (confirm('Êtes-vous sûr de vouloir retirer ce produit de vos favoris ?')) {
                fetch('/client/favoris/retirer/' + produitId, {
                    method: 'POST'
                })
                .then(response => response.text())
                .then(data => {
                    if (data === 'success') {
                        TechShop.showNotification('Produit retiré des favoris', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        TechShop.showNotification('Erreur lors de la suppression', 'error');
                    }
                })
                .catch(error => {
                    TechShop.showNotification('Erreur de connexion', 'error');
                });
            }
        }

        function ajouterTousAuPanier() {
            const produits = document.querySelectorAll('[onclick^="ajouterAuPanier"]');
            let count = 0;

            produits.forEach(button => {
                if (!button.disabled) {
                    const onclick = button.getAttribute('onclick');
                    const produitId = onclick.match(/\d+/)[0];

                    fetch('/client/panier/ajouter', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'produitId=' + produitId + '&quantite=1'
                    })
                    .then(response => response.text())
                    .then(data => {
                        if (data === 'success') {
                            count++;
                        }
                    });
                }
            });

            setTimeout(() => {
                if (count > 0) {
                    TechShop.showNotification(count + ' produit(s) ajouté(s) au panier !', 'success');
                }
            }, 1000);
        }

        function viderFavoris() {
            if (confirm('Êtes-vous sûr de vouloir vider tous vos favoris ?')) {
                // Implementation pour vider tous les favoris
                TechShop.showNotification('Fonctionnalité en cours de développement', 'info');
            }
        }

        // Debugging Bootstrap Dropdowns pour la page favoris
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS (PAGE FAVORIS) ===');

            // Attendre que Bootstrap soit chargé
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap is loaded on favoris page, version:', bootstrap.Tooltip.VERSION || 'unknown');

                    // Find all dropdown toggles
                    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                    console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles on favoris page');

                    // Initialize dropdowns manually
                    dropdownToggles.forEach((toggle, index) => {
                        console.log(`🎯 Favoris Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                        try {
                            // Create Bootstrap dropdown instance
                            const dropdown = new bootstrap.Dropdown(toggle);
                            console.log(`✅ Favoris Dropdown ${index + 1} initialized successfully`);

                            // Add event listeners for debugging
                            toggle.addEventListener('show.bs.dropdown', function() {
                                console.log(`📂 Favoris Dropdown ${index + 1} is opening`);
                            });

                            toggle.addEventListener('shown.bs.dropdown', function() {
                                console.log(`📂 Favoris Dropdown ${index + 1} is now open`);
                            });

                            toggle.addEventListener('click', function(e) {
                                console.log(`🖱️ Favoris Dropdown ${index + 1} clicked`, e);
                            });

                        } catch (error) {
                            console.error(`❌ Error initializing favoris dropdown ${index + 1}:`, error);
                        }
                    });

                } else {
                    console.error('❌ Bootstrap is not loaded on favoris page!');
                }
            }, 500); // Attendre 500ms pour que Bootstrap soit chargé
        });
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

</body>
</html>
