<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Mes Commandes - TechShop</title>
</head>
<body class="page-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <!-- Main Content Wrapper -->
    <div class="content-wrapper">
        <div class="container mt-4">
            <div class="row">
                <!-- Sidebar Client -->
                <div th:replace="~{fragments/layout :: client-sidebar}"></div>

                <!-- Main Content -->
                <div class="col-md-9">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-shopping-bag"></i> Mes Commandes</h2>
                        <div>
                            <span class="badge bg-info fs-6" th:text="${#lists.size(commandes)} + ' commande(s)'">0 commande(s)</span>
                        </div>
                    </div>

                    <!-- Messages d'alerte -->
                    <div th:if="${param.error}" class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span th:text="${param.error == 'acces-refuse' ? 'Accès refusé à cette commande' : 'Erreur inconnue'}">Erreur</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <div th:if="${param.success}" class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i>
                        <span th:text="${param.success}">Succès</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <!-- Liste des commandes -->
                    <div th:if="${#lists.isEmpty(commandes)}" class="text-center py-5">
                        <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">Aucune commande</h4>
                        <p class="text-muted">Vous n'avez pas encore passé de commande.</p>
                        <a th:href="@{/produits}" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> Commencer mes achats
                        </a>
                    </div>

                    <div th:if="${!#lists.isEmpty(commandes)}" class="row">
                        <div th:each="commande : ${commandes}" class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">
                                            <i class="fas fa-receipt"></i> 
                                            Commande #<span th:text="${commande.referenceCommande}">REF001</span>
                                        </h6>
                                        <small class="text-muted">
                                            Passée le <span th:text="${#temporals.format(commande.dateCommande, 'dd/MM/yyyy à HH:mm')}">01/01/2024 à 10:00</span>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge fs-6" 
                                              th:classappend="${commande.statutCommande == 'EN_ATTENTE' ? 'bg-warning' : 
                                                             commande.statutCommande == 'CONFIRMEE' ? 'bg-info' : 
                                                             commande.statutCommande == 'EXPEDIEE' ? 'bg-primary' : 
                                                             commande.statutCommande == 'LIVREE' ? 'bg-success' : 
                                                             commande.statutCommande == 'ANNULEE' ? 'bg-danger' : 'bg-secondary'}"
                                              th:text="${commande.statutCommande == 'EN_ATTENTE' ? 'En attente' : 
                                                       commande.statutCommande == 'CONFIRMEE' ? 'Confirmée' : 
                                                       commande.statutCommande == 'EXPEDIEE' ? 'Expédiée' : 
                                                       commande.statutCommande == 'LIVREE' ? 'Livrée' : 
                                                       commande.statutCommande == 'ANNULEE' ? 'Annulée' : commande.statutCommande}">
                                            En attente
                                        </span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <p class="mb-1">
                                                <strong>Total : </strong>
                                                <span class="text-primary fw-bold" th:text="${#numbers.formatDecimal(commande.coutCommande, 0, 2)} + ' €'">0,00 €</span>
                                            </p>
                                            <p class="mb-1" th:if="${commande.paiement != null}">
                                                <strong>Paiement : </strong>
                                                <span class="badge" 
                                                      th:classappend="${commande.paiement.estPaye ? 'bg-success' : 'bg-warning'}"
                                                      th:text="${commande.paiement.estPaye ? 'Payé' : 'En attente'}">
                                                    En attente
                                                </span>
                                                <small class="text-muted" th:if="${commande.paiement.modePaiement != null}">
                                                    (<span th:text="${commande.paiement.modePaiement}">CB</span>)
                                                </small>
                                            </p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <a th:href="@{/client/commandes/{id}(id=${commande.id})}" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i> Voir détails
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

</body>
</html>
