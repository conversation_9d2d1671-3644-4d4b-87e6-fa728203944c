<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Détail Commande - TechShop</title>
</head>
<body class="page-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <!-- Main Content Wrapper -->
    <div class="content-wrapper">
        <div class="container mt-4">
            <div class="row">
                <!-- Sidebar Client -->
                <div th:replace="~{fragments/layout :: client-sidebar}"></div>

                <!-- Main Content -->
                <div class="col-md-9">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-receipt"></i> D<PERSON><PERSON> de la Commande</h2>
                        <a th:href="@{/client/commandes}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à mes commandes
                        </a>
                    </div>

                    <!-- Informations de la commande -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Informations générales</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>Référence :</strong> <span th:text="${commande.referenceCommande}">REF001</span></p>
                                    <p><strong>Date :</strong> <span th:text="${#temporals.format(commande.dateCommande, 'dd/MM/yyyy à HH:mm')}">01/01/2024 à 10:00</span></p>
                                    <p><strong>Statut :</strong> 
                                        <span class="badge fs-6" 
                                              th:classappend="${commande.statutCommande == 'EN_ATTENTE' ? 'bg-warning' : 
                                                             commande.statutCommande == 'CONFIRMEE' ? 'bg-info' : 
                                                             commande.statutCommande == 'EXPEDIEE' ? 'bg-primary' : 
                                                             commande.statutCommande == 'LIVREE' ? 'bg-success' : 
                                                             commande.statutCommande == 'ANNULEE' ? 'bg-danger' : 'bg-secondary'}"
                                              th:text="${commande.statutCommande == 'EN_ATTENTE' ? 'En attente' : 
                                                       commande.statutCommande == 'CONFIRMEE' ? 'Confirmée' : 
                                                       commande.statutCommande == 'EXPEDIEE' ? 'Expédiée' : 
                                                       commande.statutCommande == 'LIVREE' ? 'Livrée' : 
                                                       commande.statutCommande == 'ANNULEE' ? 'Annulée' : commande.statutCommande}">
                                            En attente
                                        </span>
                                    </p>
                                    <p><strong>Total :</strong> <span class="text-primary fw-bold" th:text="${#numbers.formatDecimal(commande.coutCommande, 0, 2)} + ' €'">0,00 €</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-credit-card"></i> Paiement</h5>
                                </div>
                                <div class="card-body">
                                    <div th:if="${commande.paiement != null}">
                                        <p><strong>Statut :</strong> 
                                            <span class="badge fs-6" 
                                                  th:classappend="${commande.paiement.estPaye ? 'bg-success' : 'bg-warning'}"
                                                  th:text="${commande.paiement.estPaye ? 'Payé' : 'En attente'}">
                                                En attente
                                            </span>
                                        </p>
                                        <p th:if="${commande.paiement.modePaiement != null}">
                                            <strong>Mode :</strong> <span th:text="${commande.paiement.modePaiement}">CB</span>
                                        </p>
                                        <p th:if="${commande.paiement.datePaiement != null}">
                                            <strong>Date :</strong> <span th:text="${#temporals.format(commande.paiement.datePaiement, 'dd/MM/yyyy à HH:mm')}">01/01/2024 à 10:00</span>
                                        </p>
                                    </div>
                                    <div th:if="${commande.paiement == null}">
                                        <p class="text-muted">Aucune information de paiement disponible</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Produits commandés -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-box"></i> Produits commandés</h5>
                        </div>
                        <div class="card-body">
                            <div th:if="${#lists.isEmpty(lignesCommande)}" class="text-center py-3">
                                <p class="text-muted">Aucun produit dans cette commande</p>
                            </div>
                            
                            <div th:if="${!#lists.isEmpty(lignesCommande)}">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Produit</th>
                                                <th>Prix unitaire</th>
                                                <th>Quantité</th>
                                                <th>Sous-total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr th:each="ligne : ${lignesCommande}">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img th:if="${ligne.produit.urlImage != null}" 
                                                             th:src="@{${ligne.produit.urlImage}}" 
                                                             alt="Image produit" 
                                                             class="me-3" 
                                                             style="width: 50px; height: 50px; object-fit: cover;">
                                                        <div th:if="${ligne.produit.urlImage == null}" 
                                                             class="me-3 bg-light d-flex align-items-center justify-content-center" 
                                                             style="width: 50px; height: 50px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0" th:text="${ligne.produit.designation}">Nom du produit</h6>
                                                            <small class="text-muted" th:text="${ligne.produit.marque}">Marque</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td th:text="${#numbers.formatDecimal(ligne.produit.prix, 0, 2)} + ' €'">0,00 €</td>
                                                <td th:text="${ligne.quantite}">1</td>
                                                <td class="fw-bold" th:text="${#numbers.formatDecimal(ligne.produit.prix * ligne.quantite, 0, 2)} + ' €'">0,00 €</td>
                                            </tr>
                                        </tbody>
                                        <tfoot class="table-light">
                                            <tr>
                                                <th colspan="3" class="text-end">Total :</th>
                                                <th class="text-primary" th:text="${#numbers.formatDecimal(commande.coutCommande, 0, 2)} + ' €'">0,00 €</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

</body>
</html>
