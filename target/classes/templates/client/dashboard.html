<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Mon Compte - TechShop</title>
</head>
<body class="client-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <div class="client-main">
        <div class="container mt-4">
            <div class="row">
                <!-- Sidebar Client -->
                <div th:replace="~{fragments/layout :: client-sidebar}"></div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-user-circle"></i> Bienvenue dans votre espace client</h2>
                    <span class="badge bg-primary">Client</span>
                </div>

                <!-- Welcome Message -->
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> Bonjour <span sec:authentication="name">Client</span> !</h5>
                    <p class="mb-0">Bienvenue dans votre espace personnel. Vous pouvez gérer vos commandes, vos favoris et vos informations personnelles.</p>
                </div>

                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-bag fa-2x mb-2"></i>
                                <h4>0</h4>
                                <p class="mb-0">Commandes</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-heart fa-2x mb-2"></i>
                                <h4>0</h4>
                                <p class="mb-0">Favoris</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                <h4>0</h4>
                                <p class="mb-0">Panier</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                                <h4>0</h4>
                                <p class="mb-0">Adresses</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-shopping-bag"></i> Dernières Commandes</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Aucune commande récente.</p>
                                <a th:href="@{/produits}" class="btn btn-primary">
                                    <i class="fas fa-shopping-bag"></i> Commencer mes achats
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-heart"></i> Produits Favoris</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Aucun produit en favoris.</p>
                                <a th:href="@{/produits}" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i> Découvrir nos produits
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Featured Products for Client -->
                <div class="mt-4">
                    <h4><i class="fas fa-star"></i> Produits Recommandés</h4>
                    <div class="row" th:if="${produits}">
                        <div class="col-md-4 mb-3" th:each="produit : ${#lists.size(produits) > 6 ? produits.subList(0, 6) : produits}">
                            <div class="card h-100">
                                <img th:src="${produit.urlImage} ?: 'https://via.placeholder.com/250x200'"
                                     class="card-img-top" alt="Produit" style="height: 150px; object-fit: cover;">
                                <div class="card-body">
                                    <h6 class="card-title" th:text="${produit.designation}">Nom Produit</h6>
                                    <p class="card-text">
                                        <small class="text-muted" th:text="${produit.marque}">Marque</small><br>
                                        <span class="fw-bold text-primary" th:text="${#numbers.formatDecimal(produit.prix, 0, 2)} + ' €'">Prix</span>
                                    </p>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex justify-content-between">
                                        <a th:href="@{/produits/{id}(id=${produit.id})}" class="btn btn-outline-primary btn-sm" title="Voir le produit">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-primary btn-sm" sec:authorize="hasRole('CLIENT')"
                                                th:disabled="${produit.qteStock == 0}"
                                                th:onclick="|ajouterAuPanier(${produit.id}, 1)|">
                                            <i class="fas fa-cart-plus"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" sec:authorize="hasRole('CLIENT')"
                                                th:onclick="|toggleFavori(${produit.id})|">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div> <!-- End row -->
        </div> <!-- End container -->
    </div> <!-- End client-main -->

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

</body>
</html>
