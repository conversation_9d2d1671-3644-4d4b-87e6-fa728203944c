<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Catalogue Produits - TechShop</title>

    <!-- Styles personnalisés pour le dropdown panier -->
    <style>
        /* Dropdown panier */
        .panier-dropdown {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .panier-dropdown .dropdown-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }

        .ligne-panier {
            transition: background-color 0.2s ease;
        }

        .ligne-panier:hover {
            background-color: #f8f9fa;
        }

        .ligne-panier img {
            border: 1px solid #dee2e6;
        }

        .supprimer-ligne-panier {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .supprimer-ligne-panier:hover {
            opacity: 1;
        }

        .panier-footer {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        .panier-total {
            font-size: 1.1em;
        }

        /* Animation pour le compteur de panier */
        .cart-counter {
            transition: all 0.3s ease;
        }

        .cart-counter.updated {
            animation: pulse 0.6s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* Responsive pour le dropdown panier */
        @media (max-width: 576px) {
            .panier-dropdown {
                min-width: 300px !important;
                max-width: 90vw !important;
            }

            .ligne-panier h6 {
                font-size: 0.9em;
            }

            .ligne-panier small {
                font-size: 0.75em;
            }
        }
    </style>
</head>
<body class="page-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <!-- Main Content Wrapper -->
    <div class="content-wrapper">
        <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Accueil</a></li>
                <li class="breadcrumb-item active">Produits</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Sidebar Filtres -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-filter"></i> Filtres</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/produits}" method="get">
                            <!-- Recherche -->
                            <div class="mb-3">
                                <label for="search" class="form-label">Recherche</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       th:value="${search}" placeholder="Rechercher un produit...">
                            </div>

                            <!-- Catégorie -->
                            <div class="mb-3">
                                <label for="categorieId" class="form-label">Catégorie</label>
                                <select class="form-select" id="categorieId" name="categorieId">
                                    <option value="">Toutes les catégories</option>
                                    <option th:each="categorie : ${categories}"
                                            th:value="${categorie.id}"
                                            th:text="${categorie.nomCategorie}"
                                            th:selected="${selectedCategorieId == categorie.id}">
                                        Catégorie
                                    </option>
                                </select>
                            </div>

                            <!-- Prix -->
                            <div class="mb-3">
                                <label class="form-label">Prix</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="minPrix"
                                               th:value="${minPrix}" placeholder="Min" step="0.01">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="maxPrix"
                                               th:value="${maxPrix}" placeholder="Max" step="0.01">
                                    </div>
                                </div>
                            </div>

                            <!-- Tri -->
                            <div class="mb-3">
                                <label for="sortBy" class="form-label">Trier par</label>
                                <select class="form-select" id="sortBy" name="sortBy">
                                    <option value="designation" th:selected="${sortBy == 'designation'}">Nom</option>
                                    <option value="prix" th:selected="${sortBy == 'prix'}">Prix</option>
                                    <option value="creeLe" th:selected="${sortBy == 'creeLe'}">Date</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <select class="form-select" name="sortDir">
                                    <option value="asc" th:selected="${sortDir == 'asc'}">Croissant</option>
                                    <option value="desc" th:selected="${sortDir == 'desc'}">Décroissant</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Filtrer
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Catégories populaires -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6>Catégories populaires</h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <a th:each="categorie : ${#lists.size(categories) > 5 ? categories.subList(0, 5) : categories}"
                           th:href="@{/produits/categorie/{id}(id=${categorie.id})}"
                           class="list-group-item list-group-item-action">
                            <i class="fas fa-tag"></i> <span th:text="${categorie.nomCategorie}">Catégorie</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Liste des produits -->
            <div class="col-md-9">
                <!-- En-tête -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-box"></i> Catalogue Produits
                        <span class="badge bg-secondary status-badge" th:if="${produits.content}" th:text="${produits.totalElements}">0</span>
                    </h2>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary active" id="gridView">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="listView">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- Produits Grid -->
                <div class="row" id="productsGrid" th:if="${produits}">
                    <div class="col-md-4 mb-4" th:each="produit : ${produits}">
                        <div class="card h-100 product-card">
                            <img th:src="${produit.urlImage} ?: 'https://via.placeholder.com/300x200'"
                                 class="card-img-top" alt="Produit" style="height: 200px; object-fit: cover;">

                            <div class="card-body">
                                <h6 class="card-title" th:text="${produit.designation}">Nom Produit</h6>
                                <p class="card-text">
                                    <small class="text-muted" th:text="${produit.marque}">Marque</small><br>
                                    <span class="badge bg-info status-badge" th:text="${produit.categorie?.nomCategorie}">Catégorie</span>
                                </p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 text-primary mb-0" th:text="${#numbers.formatDecimal(produit.prix, 0, 2)} + ' €'">Prix</span>
                                    <div>
                                        <span class="badge bg-success status-badge" th:if="${produit.qteStock > 0}">
                                            <i class="fas fa-check"></i> En stock
                                        </span>
                                        <span class="badge bg-danger status-badge" th:if="${produit.qteStock == 0}">
                                            <i class="fas fa-times"></i> Rupture
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <!-- Bouton Voir le produit (visible pour tous) -->
                                    <a th:href="@{/produits/{id}(id=${produit.id})}" class="btn btn-outline-primary btn-sm" title="Voir le produit">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                    <!-- Bouton Ajouter au panier pour clients connectés -->
                                    <button class="btn btn-primary btn-sm ajouter-panier-btn"
                                            sec:authorize="hasRole('CLIENT')"
                                            th:disabled="${produit.qteStock == 0}"
                                            th:data-produit-id="${produit.id}"
                                            th:data-produit-nom="${produit.designation}"
                                            th:data-produit-prix="${produit.prix}"
                                            title="Ajouter au panier">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>

                                    <!-- Bouton Ajouter au panier pour utilisateurs non connectés -->
                                    <button class="btn btn-primary btn-sm ajouter-panier-anonyme-btn"
                                            sec:authorize="!isAuthenticated()"
                                            th:disabled="${produit.qteStock == 0}"
                                            th:data-produit-id="${produit.id}"
                                            th:data-produit-nom="${produit.designation}"
                                            th:data-produit-prix="${produit.prix}"
                                            title="Ajouter au panier (connexion requise)">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>

                                    <!-- Bouton Favoris pour clients connectés -->
                                    <button class="btn btn-outline-danger btn-sm"
                                            sec:authorize="hasRole('CLIENT')"
                                            th:onclick="|toggleFavori(${produit.id})|"
                                            title="Ajouter aux favoris">
                                        <i class="far fa-heart"></i>
                                    </button>

                                    <!-- Bouton Favoris pour utilisateurs non connectés (redirection vers login) -->
                                    <a th:href="@{/login}"
                                       class="btn btn-outline-danger btn-sm"
                                       sec:authorize="!isAuthenticated()"
                                       title="Connectez-vous pour ajouter aux favoris">
                                        <i class="far fa-heart"></i>
                                    </a>

                                    <!-- Bouton Admin pour gestion produit -->
                                    <a th:href="@{/admin/produits/{id}(id=${produit.id})}"
                                       class="btn btn-outline-secondary btn-sm"
                                       sec:authorize="hasRole('ADMIN')"
                                       title="Gérer ce produit">
                                        <i class="fas fa-cog"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <nav th:if="${produits.totalPages > 1}">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" th:classappend="${produits.first} ? 'disabled'">
                            <a class="page-link" th:href="@{/produits(page=${currentPage - 1}, size=${produits.size}, sortBy=${sortBy}, sortDir=${sortDir})}">
                                Précédent
                            </a>
                        </li>

                        <li class="page-item" th:each="i : ${#numbers.sequence(0, produits.totalPages - 1)}"
                            th:classappend="${i == currentPage} ? 'active'">
                            <a class="page-link" th:href="@{/produits(page=${i}, size=${produits.size}, sortBy=${sortBy}, sortDir=${sortDir})}"
                               th:text="${i + 1}">1</a>
                        </li>

                        <li class="page-item" th:classappend="${produits.last} ? 'disabled'">
                            <a class="page-link" th:href="@{/produits(page=${currentPage + 1}, size=${produits.size}, sortBy=${sortBy}, sortDir=${sortDir})}">
                                Suivant
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- Message si aucun produit -->
                <div th:if="${#lists.isEmpty(produits)}" class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucun produit trouvé</h4>
                    <p class="text-muted">Essayez de modifier vos critères de recherche.</p>
                    <a th:href="@{/produits}" class="btn btn-primary">Voir tous les produits</a>
                </div>
            </div>
        </div>
        </div> <!-- End container -->
    </div> <!-- End content-wrapper -->

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

    <script>
        // Debugging Bootstrap Dropdowns pour la page produits liste
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS (PAGE PRODUITS LISTE) ===');

            // Attendre que Bootstrap soit chargé
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap is loaded on produits liste page, version:', bootstrap.Tooltip.VERSION || 'unknown');

                    // Find all dropdown toggles
                    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                    console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles on produits liste page');

                    // Initialize dropdowns manually
                    dropdownToggles.forEach((toggle, index) => {
                        console.log(`🎯 Produits Liste Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                        try {
                            // Create Bootstrap dropdown instance
                            const dropdown = new bootstrap.Dropdown(toggle);
                            console.log(`✅ Produits Liste Dropdown ${index + 1} initialized successfully`);

                            // Add event listeners for debugging
                            toggle.addEventListener('show.bs.dropdown', function() {
                                console.log(`📂 Produits Liste Dropdown ${index + 1} is opening`);
                            });

                            toggle.addEventListener('shown.bs.dropdown', function() {
                                console.log(`📂 Produits Liste Dropdown ${index + 1} is now open`);
                            });

                            toggle.addEventListener('click', function(e) {
                                console.log(`🖱️ Produits Liste Dropdown ${index + 1} clicked`, e);
                            });

                        } catch (error) {
                            console.error(`❌ Error initializing produits liste dropdown ${index + 1}:`, error);
                        }
                    });

                } else {
                    console.error('❌ Bootstrap is not loaded on produits liste page!');
                }
            }, 500); // Attendre 500ms pour que Bootstrap soit chargé

            // ==================== LOGIQUE PANIER ====================

            // Gestion des boutons d'ajout au panier pour clients connectés
            const boutonsAjouterPanier = document.querySelectorAll('.ajouter-panier-btn');
            console.log('🛒 Found', boutonsAjouterPanier.length, 'boutons ajouter au panier (clients)');

            boutonsAjouterPanier.forEach(bouton => {
                bouton.addEventListener('click', function(e) {
                    e.preventDefault();

                    const produitId = this.getAttribute('data-produit-id');
                    const produitNom = this.getAttribute('data-produit-nom');
                    const produitPrix = this.getAttribute('data-produit-prix');

                    console.log('🛒 Ajout au panier (client):', { produitId, produitNom, produitPrix });

                    ajouterAuPanierClient(this, produitId, produitNom);
                });
            });

            // Gestion des boutons d'ajout au panier pour utilisateurs anonymes
            const boutonsAjouterPanierAnonyme = document.querySelectorAll('.ajouter-panier-anonyme-btn');
            console.log('🛒 Found', boutonsAjouterPanierAnonyme.length, 'boutons ajouter au panier (anonymes)');

            boutonsAjouterPanierAnonyme.forEach(bouton => {
                bouton.addEventListener('click', function(e) {
                    e.preventDefault();

                    const produitId = this.getAttribute('data-produit-id');
                    const produitNom = this.getAttribute('data-produit-nom');
                    const produitPrix = this.getAttribute('data-produit-prix');

                    console.log('🛒 Ajout au panier (anonyme):', { produitId, produitNom, produitPrix });

                    ajouterAuPanierAnonyme(this, produitId, produitNom);
                });
            });
        });

        // Fonction pour ajouter au panier (clients connectés)
        function ajouterAuPanierClient(bouton, produitId, produitNom) {
            // Désactiver le bouton temporairement
            const boutonOriginal = bouton.innerHTML;
            bouton.disabled = true;
            bouton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Récupérer le token CSRF
            const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');

            // Préparer les headers avec CSRF
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            };
            if (csrfToken && csrfHeader) {
                headers[csrfHeader] = csrfToken;
            }

            // Appel AJAX pour ajouter au panier
            fetch('/panier/ajouter', {
                method: 'POST',
                headers: headers,
                body: `produitId=${produitId}&quantite=1`
            })
            .then(response => {
                if (response.ok) {
                    return response.text();
                }
                throw new Error('Erreur lors de l\'ajout au panier');
            })
            .then(data => {
                console.log('✅ Produit ajouté au panier avec succès');

                // Afficher un message de succès
                bouton.innerHTML = '<i class="fas fa-check"></i>';
                bouton.classList.remove('btn-primary');
                bouton.classList.add('btn-success');

                // Mettre à jour le compteur du panier
                mettreAJourCompteurPanier();

                // Recharger le contenu du dropdown panier si ouvert
                if (document.querySelector('.panier-dropdown.show')) {
                    setTimeout(() => chargerContenuPanier(), 500);
                }

                // Remettre le bouton normal après 2 secondes
                setTimeout(() => {
                    bouton.innerHTML = boutonOriginal;
                    bouton.classList.remove('btn-success');
                    bouton.classList.add('btn-primary');
                    bouton.disabled = false;
                }, 2000);

                // Afficher une notification toast
                afficherToast(`${produitNom} ajouté au panier !`, 'success');
            })
            .catch(error => {
                console.error('❌ Erreur ajout panier:', error);

                // Remettre le bouton normal
                bouton.innerHTML = boutonOriginal;
                bouton.disabled = false;

                // Afficher une notification d'erreur
                afficherToast('Erreur lors de l\'ajout au panier', 'error');
            });
        }

        // Fonction pour ajouter au panier (utilisateurs anonymes)
        function ajouterAuPanierAnonyme(bouton, produitId, produitNom) {
            // Désactiver le bouton temporairement
            const boutonOriginal = bouton.innerHTML;
            bouton.disabled = true;
            bouton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Récupérer le token CSRF
            const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');

            // Préparer les headers avec CSRF
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            };
            if (csrfToken && csrfHeader) {
                headers[csrfHeader] = csrfToken;
            }

            // Appel AJAX pour ajouter au panier (endpoint public)
            fetch('/panier/ajouter', {
                method: 'POST',
                headers: headers,
                body: `produitId=${produitId}&quantite=1`
            })
            .then(response => {
                if (response.ok) {
                    return response.text();
                }
                throw new Error('Erreur lors de l\'ajout au panier');
            })
            .then(data => {
                console.log('✅ Produit ajouté au panier anonyme avec succès');

                // Afficher un message de succès
                bouton.innerHTML = '<i class="fas fa-check"></i>';
                bouton.classList.remove('btn-primary');
                bouton.classList.add('btn-success');

                // Mettre à jour le compteur du panier
                mettreAJourCompteurPanierAnonyme();

                // Recharger le contenu du dropdown panier si ouvert
                if (document.querySelector('.panier-dropdown.show')) {
                    setTimeout(() => chargerContenuPanier(), 500);
                }

                // Remettre le bouton normal après 2 secondes
                setTimeout(() => {
                    bouton.innerHTML = boutonOriginal;
                    bouton.classList.remove('btn-success');
                    bouton.classList.add('btn-primary');
                    bouton.disabled = false;
                }, 2000);

                // Afficher une notification toast avec option de connexion
                afficherToastAvecConnexion(`${produitNom} ajouté au panier !`, 'success');

            })
            .catch(error => {
                console.error('❌ Erreur ajout panier anonyme:', error);

                // Remettre le bouton normal
                bouton.innerHTML = boutonOriginal;
                bouton.disabled = false;

                // Afficher une notification d'erreur
                afficherToast('Erreur lors de l\'ajout au panier', 'error');
            });
        }

        // Fonction pour mettre à jour le compteur du panier
        function mettreAJourCompteurPanier() {
            fetch('/panier/compteur')
                .then(response => response.json())
                .then(compteur => {
                    console.log('🔄 Mise à jour compteur panier:', compteur);

                    // Mettre à jour tous les compteurs de panier
                    const compteurs = document.querySelectorAll('.cart-counter');
                    compteurs.forEach(element => {
                        element.textContent = compteur;
                        // Toujours afficher le compteur pour les utilisateurs anonymes
                        element.style.display = '';
                        element.classList.remove('d-none');

                        // Ajouter une animation quand le compteur change
                        element.classList.add('updated');
                        setTimeout(() => {
                            element.classList.remove('updated');
                        }, 600);
                    });
                })
                .catch(error => {
                    console.error('❌ Erreur mise à jour compteur:', error);
                });
        }

        // Fonction pour mettre à jour le compteur du panier (utilisateurs anonymes)
        function mettreAJourCompteurPanierAnonyme() {
            fetch('/panier/compteur')
                .then(response => response.json())
                .then(compteur => {
                    console.log('🔄 Mise à jour compteur panier anonyme:', compteur);

                    // Mettre à jour tous les compteurs de panier
                    const compteurs = document.querySelectorAll('.cart-counter');
                    compteurs.forEach(element => {
                        element.textContent = compteur;
                        // Toujours afficher le compteur pour les utilisateurs anonymes
                        element.style.display = '';
                        element.classList.remove('bg-danger');
                        element.classList.add('bg-warning'); // Couleur différente pour panier anonyme

                        // Ajouter une animation quand le compteur change
                        element.classList.add('updated');
                        setTimeout(() => {
                            element.classList.remove('updated');
                        }, 600);
                    });
                })
                .catch(error => {
                    console.error('❌ Erreur mise à jour compteur anonyme:', error);
                });
        }

        // Fonction pour afficher des notifications toast
        function afficherToast(message, type = 'info') {
            // Créer un élément toast
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Ajouter au body
            document.body.appendChild(toast);

            // Supprimer automatiquement après 3 secondes
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        // Fonction pour afficher des notifications toast avec option de connexion
        function afficherToastAvecConnexion(message, type = 'info') {
            // Créer un élément toast
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'warning' : 'danger'} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'exclamation-triangle' : 'exclamation-circle'}"></i>
                ${message}
                <br><small>Connectez-vous pour sauvegarder votre panier.</small>
                <div class="mt-2">
                    <a href="/login" class="btn btn-sm btn-primary me-2">Se connecter</a>
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-dismiss="alert">Continuer</button>
                </div>
            `;

            // Ajouter au body
            document.body.appendChild(toast);

            // Supprimer automatiquement après 5 secondes
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }

        // Fonction pour initialiser immédiatement le compteur du panier avec les données serveur
        function initialiserCompteurPanierImmediat() {
            // Récupérer la valeur du compteur depuis l'attribut data ou le contenu existant
            const compteurs = document.querySelectorAll('.cart-counter');

            compteurs.forEach(element => {
                // Récupérer la valeur initiale depuis l'attribut Thymeleaf ou le contenu
                const valeurInitiale = element.textContent.trim();
                const compteur = parseInt(valeurInitiale) || 0;

                console.log('🚀 Initialisation immédiate compteur panier (page produits):', compteur);

                // Afficher immédiatement le compteur sans attendre d'appel AJAX
                element.textContent = compteur;

                // Toujours afficher le compteur pour les utilisateurs anonymes
                element.style.display = '';
                element.classList.remove('d-none');
            });
        }

        // Initialisation immédiate du compteur du panier
        function initCompteurPanierImmediat() {
            // Vérifier si les éléments sont déjà disponibles
            const compteurs = document.querySelectorAll('.cart-counter');
            if (compteurs.length > 0) {
                // Utiliser d'abord l'initialisation immédiate avec les données serveur
                initialiserCompteurPanierImmediat();
                // Puis initialiser le dropdown
                initialiserDropdownPanier();
                // Éviter l'appel AJAX initial pour réduire le retard
                // mettreAJourCompteurPanierAnonyme(); // Commenté pour éviter le retard
            } else {
                // Si pas encore disponibles, réessayer dans 50ms
                setTimeout(initCompteurPanierImmediat, 50);
            }
        }

        // Initialiser le compteur du panier au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialisation immédiate sans délai
            initCompteurPanierImmediat();
        });

        // Initialisation immédiate du compteur (sans attendre DOMContentLoaded)
        initCompteurPanierImmediat();

        // ==================== DROPDOWN PANIER ====================

        // Fonction pour initialiser le dropdown du panier
        function initialiserDropdownPanier() {
            const dropdownPanier = document.querySelector('.panier-dropdown');
            if (!dropdownPanier) return;

            // Écouter l'ouverture du dropdown
            const dropdownTriggers = document.querySelectorAll('#panierDropdownClient, #panierDropdownAnonyme');
            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('show.bs.dropdown', function() {
                    console.log('🛒 Ouverture du dropdown panier');
                    chargerContenuPanier();
                });
            });
        }

        // Fonction pour charger le contenu du panier
        function chargerContenuPanier() {
            fetch('/panier/contenu')
                .then(response => response.json())
                .then(data => {
                    console.log('🛒 Contenu panier reçu:', data);
                    afficherContenuPanier(data);
                })
                .catch(error => {
                    console.error('❌ Erreur chargement contenu panier:', error);
                    afficherErreurPanier();
                });
        }

        // Fonction pour afficher le contenu du panier dans le dropdown
        function afficherContenuPanier(data) {
            const panierContenu = document.getElementById('panier-contenu');
            const panierCompteurText = document.querySelector('.panier-compteur-text');
            const panierTotal = document.querySelector('.panier-total');
            const panierFooter = document.querySelector('.panier-footer');
            const panierFooterDivider = document.querySelector('.panier-footer-divider');
            const panierVide = document.querySelector('.panier-vide');

            if (!panierContenu) return;

            // Mettre à jour le compteur dans le header
            if (panierCompteurText) {
                const nombreArticles = data.nombreArticles || 0;
                panierCompteurText.textContent = `${nombreArticles} article${nombreArticles > 1 ? 's' : ''}`;
            }

            // Mettre à jour le total
            if (panierTotal) {
                const total = data.total || 0;
                panierTotal.textContent = `${total.toFixed(2)} €`;
            }

            if (data.vide || !data.lignes || data.lignes.length === 0) {
                // Panier vide
                panierContenu.innerHTML = `
                    <li class="px-3 py-2 text-center text-muted panier-vide">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <p class="mb-0">Votre panier est vide</p>
                        <small>Ajoutez des produits pour commencer vos achats</small>
                    </li>
                `;

                // Masquer le footer
                if (panierFooter) panierFooter.style.display = 'none';
                if (panierFooterDivider) panierFooterDivider.style.display = 'none';
            } else {
                // Panier avec des articles
                let contenuHTML = '';

                data.lignes.forEach(ligne => {
                    const imageUrl = ligne.urlImage || '/images/default-product.jpg';
                    const sousTotal = ligne.sousTotal || (ligne.produitPrix * ligne.quantite);

                    contenuHTML += `
                        <li class="px-3 py-2 border-bottom ligne-panier" data-produit-id="${ligne.produitId}">
                            <div class="d-flex align-items-center">
                                <img src="${imageUrl}"
                                     alt="${ligne.produitNom}"
                                     class="rounded me-2"
                                     style="width: 40px; height: 40px; object-fit: cover;"
                                     onerror="this.src='/images/default-product.jpg'">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0 text-truncate" style="max-width: 180px;" title="${ligne.produitNom}">
                                        ${ligne.produitNom}
                                    </h6>
                                    <small class="text-muted">
                                        ${ligne.quantite} × ${ligne.produitPrix.toFixed(2)} €
                                    </small>
                                </div>
                                <div class="text-end">
                                    <strong class="text-primary">${sousTotal.toFixed(2)} €</strong>
                                    <br>
                                    <button class="btn btn-outline-danger btn-sm supprimer-ligne-panier"
                                            data-produit-id="${ligne.produitId}"
                                            title="Supprimer">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </li>
                    `;
                });

                panierContenu.innerHTML = contenuHTML;

                // Afficher le footer
                if (panierFooter) panierFooter.style.display = 'block';
                if (panierFooterDivider) panierFooterDivider.style.display = 'block';

                // Ajouter les événements pour supprimer les lignes
                ajouterEvenementsSuppressionLignes();
            }
        }

        // Fonction pour ajouter les événements de suppression des lignes
        function ajouterEvenementsSuppressionLignes() {
            const boutonsSupprimer = document.querySelectorAll('.supprimer-ligne-panier');
            boutonsSupprimer.forEach(bouton => {
                bouton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const produitId = this.getAttribute('data-produit-id');
                    supprimerLignePanier(produitId);
                });
            });
        }

        // Fonction pour supprimer une ligne du panier
        function supprimerLignePanier(produitId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cet article du panier ?')) {
                return;
            }

            // Récupérer le token CSRF
            const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');

            // Préparer les headers avec CSRF
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            };
            if (csrfToken && csrfHeader) {
                headers[csrfHeader] = csrfToken;
            }

            fetch(`/panier/supprimer/${produitId}`, {
                method: 'POST',
                headers: headers
            })
            .then(response => response.text())
            .then(data => {
                if (data.includes('success')) {
                    console.log('✅ Ligne supprimée du panier');

                    // Recharger le contenu du panier
                    chargerContenuPanier();

                    // Mettre à jour le compteur
                    mettreAJourCompteurPanierAnonyme();

                    // Afficher une notification
                    afficherToast('Article supprimé du panier', 'success');
                } else {
                    throw new Error('Erreur lors de la suppression');
                }
            })
            .catch(error => {
                console.error('❌ Erreur suppression ligne panier:', error);
                afficherToast('Erreur lors de la suppression', 'error');
            });
        }

        // Fonction pour afficher une erreur dans le dropdown panier
        function afficherErreurPanier() {
            const panierContenu = document.getElementById('panier-contenu');
            if (panierContenu) {
                panierContenu.innerHTML = `
                    <li class="px-3 py-2 text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p class="mb-0">Erreur de chargement</p>
                        <small>Impossible de charger le contenu du panier</small>
                    </li>
                `;
            }
        }
    </script>

</body>
</html>
