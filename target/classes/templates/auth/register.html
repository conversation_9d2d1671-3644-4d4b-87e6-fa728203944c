<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Inscription - TechShop</title>
</head>
<body class="page-wrapper auth-page">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <div class="content-wrapper">
        <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-user-plus"></i> Créer un compte</h4>
                    </div>
                    <div class="card-body">
                        <!-- Messages d'erreur -->
                        <div class="alert alert-danger" th:if="${error}" th:text="${error}">
                        </div>

                        <form th:action="@{/register}" method="post" th:object="${utilisateur}">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="prenom" class="form-label">Prénom *</label>
                                        <input type="text" class="form-control" id="prenom" th:field="*{prenom}"
                                               placeholder="Votre prénom" required>
                                        <div class="text-danger" th:if="${#fields.hasErrors('prenom')}" th:errors="*{prenom}"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="nom" class="form-label">Nom *</label>
                                        <input type="text" class="form-control" id="nom" th:field="*{nom}"
                                               placeholder="Votre nom" required>
                                        <div class="text-danger" th:if="${#fields.hasErrors('nom')}" th:errors="*{nom}"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" th:field="*{email}"
                                       placeholder="<EMAIL>" required unique>
                                <div class="invalid-feedback">Veuillez saisir un email valide et unique.</div>
                                <div class="text-danger" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="telephone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="telephone" th:field="*{telephone}"
                                       placeholder="0123456789" unique>
                                <div class="invalid-feedback">Veuillez saisir un numéro de téléphone unique.</div>
                                <div class="text-danger" th:if="${#fields.hasErrors('telephone')}" th:errors="*{telephone}"></div>
                            </div>

                            <hr>
                            <h5>Informations de connexion</h5>

                            <div class="mb-3">
                                <label for="login" class="form-label">Login *</label>
                                <input type="text" class="form-control" id="login" name="login"
                                       placeholder="Login doit être Email / Téléphone" required>
                                <small class="form-text text-muted">Identifiant unique pour la connexion (Email ou Téléphone)</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="motDePasse" class="form-label">Mot de passe *</label>
                                        <input type="password" class="form-control" id="motDePasse" name="motDePasse"
                                               placeholder="Mot de passe (min. 6 caractères)" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirmMotDePasse" class="form-label">Confirmer le mot de passe *</label>
                                        <input type="password" class="form-control" id="confirmMotDePasse" name="confirmMotDePasse"
                                               placeholder="Confirmez votre mot de passe" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="acceptConditions" required>
                                <label class="form-check-label" for="acceptConditions">
                                    J'accepte les <a href="#" target="_blank">conditions d'utilisation</a> *
                                </label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i> Créer mon compte
                                </button>
                            </div>
                        </form>

                        <hr>

                        <div class="text-center">
                            <p>Déjà un compte ?</p>
                            <a th:href="@{/login}" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt"></i> Se connecter
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Scripts pour le dropdown panier -->
    <script>
        // ==================== INITIALISATION IMMEDIATE DES COMPTEURS ====================

        // Fonction pour initialiser immédiatement le compteur favoris (utilise la fonction globale)
        function initCompteurFavorisImmediat() {
            try {
                // Utiliser la fonction globale du fichier favoris.js si disponible
                if (typeof mettreAJourCompteurFavoris === 'function') {
                    console.log('🚀 Utilisation de la fonction globale mettreAJourCompteurFavoris');
                    mettreAJourCompteurFavoris();
                } else {
                    // Fallback : utiliser l'initialisation immédiate globale si disponible
                    if (typeof initialiserCompteurFavorisImmediat === 'function') {
                        console.log('🚀 Utilisation de la fonction globale initialiserCompteurFavorisImmediat');
                        initialiserCompteurFavorisImmediat();
                    } else {
                        console.log('⚠️ Fonctions globales favoris non disponibles, attente...');
                        // Réessayer dans 100ms
                        setTimeout(initCompteurFavorisImmediat, 100);
                    }
                }
            } catch (error) {
                console.error('❌ Erreur initialisation compteur favoris:', error);
            }
        }

        // Initialiser immédiatement si les éléments sont déjà disponibles
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initCompteurFavorisImmediat);
        } else {
            initCompteurFavorisImmediat();
        }

        // ==================== DROPDOWN PANIER POUR PAGE REGISTER ====================

        // Attendre que Bootstrap soit complètement chargé
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 Initialisation dropdowns pour page register');

            // Initialiser immédiatement le compteur des favoris
            mettreAJourCompteurFavorisLocal();

            // Attendre un peu plus pour que Bootstrap soit initialisé
            setTimeout(() => {
                initPanierDropdownForAuthPage();
                initFavorisDropdownForAuthPage();
            }, 1500);
        });

        function initPanierDropdownForAuthPage() {
            console.log('🛒 Initialisation du dropdown panier pour page d\'authentification');

            // Mettre à jour le compteur du panier
            mettreAJourCompteurPanier();

            // Attacher les événements aux dropdowns panier
            const dropdownTriggers = document.querySelectorAll('#panierDropdownClient, #panierDropdownAnonyme');
            console.log('🎯 Dropdowns trouvés:', dropdownTriggers.length);

            dropdownTriggers.forEach((trigger, index) => {
                console.log(`🔧 Configuration dropdown ${index + 1}:`, trigger.id);

                // Supprimer les anciens événements pour éviter les doublons
                trigger.removeEventListener('show.bs.dropdown', handleDropdownShow);
                trigger.removeEventListener('click', handleDropdownClick);

                // Ajouter les nouveaux événements
                trigger.addEventListener('show.bs.dropdown', handleDropdownShow);
                trigger.addEventListener('click', handleDropdownClick);

                console.log(`✅ Événements attachés au dropdown ${index + 1}`);
            });
        }

        function handleDropdownShow(event) {
            console.log('🛒 Dropdown panier ouvert, chargement du contenu...');
            event.preventDefault(); // Empêcher l'ouverture automatique

            // Charger le contenu puis ouvrir manuellement
            chargerContenuPanier().then(() => {
                // Ouvrir le dropdown manuellement après chargement
                const dropdownElement = event.target.nextElementSibling;
                if (dropdownElement && dropdownElement.classList.contains('dropdown-menu')) {
                    dropdownElement.classList.add('show');
                }
            });
        }

        function handleDropdownClick(event) {
            console.log('🖱️ Clic sur dropdown panier');
            event.preventDefault();

            const dropdownMenu = event.target.closest('.dropdown').querySelector('.dropdown-menu');
            if (dropdownMenu) {
                if (dropdownMenu.classList.contains('show')) {
                    dropdownMenu.classList.remove('show');
                } else {
                    // Charger le contenu avant d'afficher
                    chargerContenuPanier().then(() => {
                        dropdownMenu.classList.add('show');
                    });
                }
            }
        }

        // Fonction pour charger le contenu du panier
        function chargerContenuPanier() {
            return fetch('/panier/contenu')
                .then(response => response.json())
                .then(data => {
                    console.log('🛒 Contenu panier reçu:', data);
                    afficherContenuPanier(data);
                    return data;
                })
                .catch(error => {
                    console.error('❌ Erreur chargement contenu panier:', error);
                    afficherErreurPanier();
                    throw error;
                });
        }

        // Fonction pour afficher le contenu du panier dans le dropdown
        function afficherContenuPanier(data) {
            const panierContenu = document.getElementById('panier-contenu');
            const panierCompteurText = document.querySelector('.panier-compteur-text');
            const panierTotal = document.querySelector('.panier-total');
            const panierFooter = document.querySelector('.panier-footer');
            const panierFooterDivider = document.querySelector('.panier-footer-divider');

            if (!panierContenu) {
                console.error('❌ Élément panier-contenu non trouvé');
                return;
            }

            // Mettre à jour le compteur dans le header
            if (panierCompteurText) {
                const nombreArticles = data.nombreArticles || 0;
                panierCompteurText.textContent = `${nombreArticles} article${nombreArticles > 1 ? 's' : ''}`;
            }

            // Mettre à jour le total
            if (panierTotal) {
                const total = data.total || 0;
                panierTotal.textContent = `${total.toFixed(2)} €`;
            }

            if (data.vide || !data.lignes || data.lignes.length === 0) {
                // Panier vide
                panierContenu.innerHTML = `
                    <li class="px-3 py-2 text-center text-muted panier-vide">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <p class="mb-0">Votre panier est vide</p>
                        <small>Ajoutez des produits pour commencer vos achats</small>
                    </li>
                `;

                // Masquer le footer
                if (panierFooter) panierFooter.style.display = 'none';
                if (panierFooterDivider) panierFooterDivider.style.display = 'none';
            } else {
                // Panier avec des articles
                let contenuHTML = '';

                data.lignes.forEach(ligne => {
                    const imageUrl = ligne.urlImage || '/images/default-product.jpg';
                    const sousTotal = ligne.sousTotal || (ligne.produitPrix * ligne.quantite);

                    contenuHTML += `
                        <li class="px-3 py-2 border-bottom ligne-panier" data-produit-id="${ligne.produitId}">
                            <div class="d-flex align-items-center">
                                <img src="${imageUrl}"
                                     alt="${ligne.produitNom}"
                                     class="rounded me-2"
                                     style="width: 40px; height: 40px; object-fit: cover;"
                                     onerror="this.src='/images/default-product.jpg'">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0 text-truncate" style="max-width: 180px;" title="${ligne.produitNom}">
                                        ${ligne.produitNom}
                                    </h6>
                                    <small class="text-muted">
                                        ${ligne.quantite} × ${ligne.produitPrix.toFixed(2)} €
                                    </small>
                                </div>
                                <div class="text-end">
                                    <strong class="text-primary">${sousTotal.toFixed(2)} €</strong>
                                    <br>
                                    <button class="btn btn-outline-danger btn-sm supprimer-ligne-panier"
                                            data-produit-id="${ligne.produitId}"
                                            title="Supprimer">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </li>
                    `;
                });

                panierContenu.innerHTML = contenuHTML;

                // Afficher le footer
                if (panierFooter) panierFooter.style.display = 'block';
                if (panierFooterDivider) panierFooterDivider.style.display = 'block';

                // Ajouter les événements pour supprimer les lignes
                ajouterEvenementsSuppressionLignes();
            }
        }

        // Fonction pour ajouter les événements de suppression des lignes
        function ajouterEvenementsSuppressionLignes() {
            const boutonsSupprimer = document.querySelectorAll('.supprimer-ligne-panier');
            boutonsSupprimer.forEach(bouton => {
                bouton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const produitId = this.getAttribute('data-produit-id');
                    supprimerLignePanier(produitId);
                });
            });
        }

        // Fonction pour supprimer une ligne du panier
        function supprimerLignePanier(produitId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cet article du panier ?')) {
                return;
            }

            // Récupérer le token CSRF
            const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');

            // Préparer les headers avec CSRF
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            };
            if (csrfToken && csrfHeader) {
                headers[csrfHeader] = csrfToken;
            }

            fetch(`/panier/supprimer/${produitId}`, {
                method: 'POST',
                headers: headers
            })
            .then(response => response.text())
            .then(data => {
                if (data.includes('success')) {
                    console.log('✅ Ligne supprimée du panier');

                    // Recharger le contenu du panier
                    chargerContenuPanier();

                    // Mettre à jour le compteur
                    mettreAJourCompteurPanier();

                    // Afficher une notification
                    afficherToast('Article supprimé du panier', 'success');
                } else {
                    throw new Error('Erreur lors de la suppression');
                }
            })
            .catch(error => {
                console.error('❌ Erreur suppression ligne panier:', error);
                afficherToast('Erreur lors de la suppression', 'error');
            });
        }

        // Fonction pour afficher une erreur dans le dropdown panier
        function afficherErreurPanier() {
            const panierContenu = document.getElementById('panier-contenu');
            if (panierContenu) {
                panierContenu.innerHTML = `
                    <li class="px-3 py-2 text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p class="mb-0">Erreur de chargement</p>
                        <small>Impossible de charger le contenu du panier</small>
                    </li>
                `;
            }
        }

        // Fonction pour mettre à jour le compteur du panier
        function mettreAJourCompteurPanier() {
            fetch('/panier/compteur')
                .then(response => response.json())
                .then(compteur => {
                    console.log('🔄 Mise à jour compteur panier:', compteur);

                    // Mettre à jour tous les compteurs de panier
                    const compteurs = document.querySelectorAll('.cart-counter');
                    compteurs.forEach(element => {
                        element.textContent = compteur;
                        // Toujours afficher le compteur pour les utilisateurs anonymes
                        element.style.display = '';
                        element.classList.remove('bg-danger');
                        element.classList.add('bg-warning'); // Couleur différente pour panier anonyme

                        // Ajouter une animation quand le compteur change
                        element.classList.add('updated');
                        setTimeout(() => {
                            element.classList.remove('updated');
                        }, 600);
                    });
                })
                .catch(error => {
                    console.error('❌ Erreur mise à jour compteur:', error);
                });
        }

        // Fonction pour afficher des notifications toast
        function afficherToast(message, type = 'info') {
            // Créer un élément toast
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Ajouter au body
            document.body.appendChild(toast);

            // Supprimer automatiquement après 3 secondes
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        // ==================== DROPDOWN FAVORIS POUR PAGE REGISTER ====================

        function initFavorisDropdownForAuthPage() {
            console.log('💖 Initialisation du dropdown favoris pour page d\'authentification');

            // Mettre à jour le compteur des favoris avec fallback local
            mettreAJourCompteurFavorisLocal();

            // Attacher les événements aux dropdowns favoris
            const dropdownTriggers = document.querySelectorAll('#favorisDropdownClient, #favorisDropdownAnonyme');
            console.log('🎯 Dropdowns favoris trouvés:', dropdownTriggers.length);

            dropdownTriggers.forEach((trigger, index) => {
                console.log(`🔧 Configuration dropdown favoris ${index + 1}:`, trigger.id);

                // Supprimer les anciens événements pour éviter les doublons
                trigger.removeEventListener('show.bs.dropdown', handleFavorisDropdownShowAuth);
                trigger.removeEventListener('click', handleFavorisDropdownClickAuth);

                // Ajouter les nouveaux événements
                trigger.addEventListener('show.bs.dropdown', handleFavorisDropdownShowAuth);
                trigger.addEventListener('click', handleFavorisDropdownClickAuth);

                console.log(`✅ Événements attachés au dropdown favoris ${index + 1}`);
            });
        }

        // Fonction locale pour mettre à jour le compteur des favoris
        function mettreAJourCompteurFavorisLocal() {
            try {
                // Toujours utiliser la fonction globale d'abord
                if (typeof mettreAJourCompteurFavoris === 'function') {
                    console.log('🔄 Utilisation de la fonction globale mettreAJourCompteurFavoris');
                    mettreAJourCompteurFavoris();
                    return;
                }

                console.log('⚠️ Fonction globale mettreAJourCompteurFavoris non disponible, réessai dans 100ms');
                // Si la fonction globale n'est pas encore disponible, réessayer
                setTimeout(mettreAJourCompteurFavorisLocal, 100);
            } catch (error) {
                console.error('❌ Erreur mise à jour compteur favoris:', error);
            }
        }

        // Fonction locale pour récupérer les favoris du localStorage
        function getFavorisLocauxRegister() {
            try {
                const favoris = localStorage.getItem('techshop_favoris');
                return favoris ? JSON.parse(favoris) : [];
            } catch (error) {
                console.error('❌ Erreur lecture favoris locaux:', error);
                return [];
            }
        }

        function handleFavorisDropdownShowAuth(event) {
            console.log('💖 Dropdown favoris ouvert, chargement du contenu...');
            event.preventDefault(); // Empêcher l'ouverture automatique

            // Charger le contenu puis ouvrir manuellement
            chargerContenuFavorisLocal().then(() => {
                // Ouvrir le dropdown manuellement après chargement
                const dropdownElement = event.target.nextElementSibling;
                if (dropdownElement && dropdownElement.classList.contains('dropdown-menu')) {
                    dropdownElement.classList.add('show');
                }
            }).catch(error => {
                console.error('❌ Erreur chargement favoris:', error);
                // Ouvrir quand même le dropdown avec le contenu par défaut
                const dropdownElement = event.target.nextElementSibling;
                if (dropdownElement && dropdownElement.classList.contains('dropdown-menu')) {
                    dropdownElement.classList.add('show');
                }
            });
        }

        function handleFavorisDropdownClickAuth(event) {
            console.log('🖱️ Clic sur dropdown favoris');
            event.preventDefault();

            const dropdownMenu = event.target.closest('.dropdown').querySelector('.dropdown-menu');
            if (dropdownMenu) {
                if (dropdownMenu.classList.contains('show')) {
                    dropdownMenu.classList.remove('show');
                } else {
                    // Charger le contenu avant d'afficher
                    chargerContenuFavorisLocal().then(() => {
                        dropdownMenu.classList.add('show');
                    }).catch(error => {
                        console.error('❌ Erreur chargement favoris:', error);
                        // Afficher quand même le dropdown
                        dropdownMenu.classList.add('show');
                    });
                }
            }
        }

        // Fonction locale pour charger le contenu des favoris
        function chargerContenuFavorisLocal() {
            return new Promise((resolve, reject) => {
                try {
                    // Essayer d'utiliser la fonction globale d'abord
                    if (typeof chargerContenuFavoris === 'function') {
                        chargerContenuFavoris().then(resolve).catch(() => {
                            // Si la fonction globale échoue, utiliser le fallback local
                            chargerContenuFavorisLocalFallback();
                            resolve();
                        });
                        return;
                    }

                    // Fallback : charger les favoris locaux
                    chargerContenuFavorisLocalFallback();
                    resolve();
                } catch (error) {
                    console.error('❌ Erreur chargement favoris local:', error);
                    reject(error);
                }
            });
        }

        // Fonction de fallback pour afficher les favoris locaux
        function chargerContenuFavorisLocalFallback() {
            const favorisLocaux = getFavorisLocauxRegister();

            // Mettre à jour le compteur dans le header
            const favorisCompteurText = document.querySelector('.favoris-compteur-text');
            if (favorisCompteurText) {
                favorisCompteurText.textContent = `${favorisLocaux.length} produit${favorisLocaux.length > 1 ? 's' : ''}`;
            }

            // Déterminer quel conteneur utiliser
            let favorisContenu = document.getElementById('favoris-contenu');
            if (!favorisContenu) {
                favorisContenu = document.getElementById('favoris-contenu-anonyme');
            }

            if (!favorisContenu) {
                console.error('❌ Conteneur favoris non trouvé');
                return;
            }

            if (favorisLocaux.length === 0) {
                // Favoris vides
                favorisContenu.innerHTML = `
                    <i class="fas fa-heart fa-2x mb-2"></i>
                    <p class="mb-0">Aucun produit en favoris</p>
                    <small>Ajoutez des produits à vos favoris</small>
                `;
                favorisContenu.className = 'px-3 py-2 text-center text-muted favoris-vide';
            } else {
                // Afficher un message pour les favoris locaux
                favorisContenu.innerHTML = `
                    <i class="fas fa-heart fa-2x mb-2 text-warning"></i>
                    <p class="mb-0">${favorisLocaux.length} produit${favorisLocaux.length > 1 ? 's' : ''} en favoris</p>
                    <small class="text-muted">Connectez-vous pour voir le détail de vos favoris</small>
                `;
                favorisContenu.className = 'px-3 py-2 text-center';
            }

            console.log('📋 Contenu favoris local affiché:', favorisLocaux.length, 'produits');
        }

        // Fermer le dropdown quand on clique ailleurs
        document.addEventListener('click', function(event) {
            const dropdowns = document.querySelectorAll('.dropdown-menu.show');
            dropdowns.forEach(dropdown => {
                if (!dropdown.closest('.dropdown').contains(event.target)) {
                    dropdown.classList.remove('show');
                }
            });
        });
    </script>

    <!-- Styles personnalisés pour les dropdowns panier et favoris -->
    <style>
        /* Dropdown panier */
        .panier-dropdown {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .panier-dropdown .dropdown-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }

        .ligne-panier {
            transition: background-color 0.2s ease;
        }

        .ligne-panier:hover {
            background-color: #f8f9fa;
        }

        .ligne-panier img {
            border: 1px solid #dee2e6;
        }

        .supprimer-ligne-panier {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .supprimer-ligne-panier:hover {
            opacity: 1;
        }

        .panier-footer {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        .panier-total {
            font-size: 1.1em;
        }

        /* Animation pour le compteur de panier */
        .cart-counter {
            transition: all 0.3s ease;
        }

        .cart-counter.updated {
            animation: pulse 0.6s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* Responsive pour le dropdown panier */
        @media (max-width: 576px) {
            .panier-dropdown {
                min-width: 300px !important;
                max-width: 90vw !important;
            }

            .ligne-panier h6 {
                font-size: 0.9em;
            }

            .ligne-panier small {
                font-size: 0.75em;
            }
        }

        /* Dropdown favoris */
        .favoris-dropdown {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .favoris-dropdown .dropdown-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }

        .produit-favori {
            transition: background-color 0.2s ease;
        }

        .produit-favori:hover {
            background-color: #f8f9fa;
        }

        .produit-favori img {
            border: 1px solid #dee2e6;
        }

        .supprimer-favori, .ajouter-panier-depuis-favoris {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .supprimer-favori:hover, .ajouter-panier-depuis-favoris:hover {
            opacity: 1;
        }

        .favoris-footer {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        /* Animation pour le compteur de favoris */
        .favorites-counter {
            transition: all 0.3s ease;
        }

        .favorites-counter.updated {
            animation: pulse 0.6s ease-in-out;
        }

        /* Responsive pour le dropdown favoris */
        @media (max-width: 576px) {
            .favoris-dropdown {
                min-width: 300px !important;
                max-width: 90vw !important;
            }

            .produit-favori h6 {
                font-size: 0.9em;
            }

            .produit-favori small {
                font-size: 0.75em;
            }
        }
    </style>

    <!-- Scripts externes -->
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/panier.js}"></script>
</body>
</html>
