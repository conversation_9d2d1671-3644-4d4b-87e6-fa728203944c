// Fonctions pour la gestion des favoris

// Récupération du token CSRF
function getCsrfTokenFavoris() {
    const csrfToken = document.querySelector('meta[name="_csrf"]');
    return csrfToken ? csrfToken.getAttribute('content') : null;
}

function getCsrfHeaderFavoris() {
    const csrfHeader = document.querySelector('meta[name="_csrf_header"]');
    return csrfHeader ? csrfHeader.getAttribute('content') : 'X-CSRF-TOKEN';
}

// ==================== GESTION DES FAVORIS LOCAUX (ANONYMES) ====================

/**
 * Récupère les favoris depuis le localStorage pour les utilisateurs anonymes
 */
function getFavorisLocaux() {
    try {
        const favoris = localStorage.getItem('techshop_favoris');
        return favoris ? JSON.parse(favoris) : [];
    } catch (error) {
        console.error('❌ Erreur lecture favoris locaux:', error);
        return [];
    }
}

/**
 * Sauvegarde les favoris dans le localStorage pour les utilisateurs anonymes
 */
function saveFavorisLocaux(favoris) {
    try {
        localStorage.setItem('techshop_favoris', JSON.stringify(favoris));
        return true;
    } catch (error) {
        console.error('❌ Erreur sauvegarde favoris locaux:', error);
        return false;
    }
}

/**
 * Ajoute un produit aux favoris locaux
 */
function ajouterFavoriLocal(produitId) {
    const favoris = getFavorisLocaux();
    if (!favoris.includes(produitId)) {
        favoris.push(produitId);
        saveFavorisLocaux(favoris);
        return true;
    }
    return false;
}

/**
 * Supprime un produit des favoris locaux
 */
function supprimerFavoriLocal(produitId) {
    const favoris = getFavorisLocaux();
    const index = favoris.indexOf(produitId);
    if (index > -1) {
        favoris.splice(index, 1);
        saveFavorisLocaux(favoris);
        return true;
    }
    return false;
}

// ==================== API FAVORIS ====================

/**
 * Initialise immédiatement le compteur des favoris avec les données serveur
 */
function initialiserCompteurFavorisImmediat() {
    // Récupérer la valeur du compteur depuis l'attribut data ou le contenu existant
    const compteurs = document.querySelectorAll('.favorites-counter');

    compteurs.forEach(element => {
        // Récupérer la valeur initiale depuis l'attribut Thymeleaf ou le contenu
        const valeurInitiale = element.textContent.trim();
        const compteur = parseInt(valeurInitiale) || 0;

        console.log('🚀 Initialisation immédiate compteur favoris:', compteur);

        // Afficher immédiatement le compteur sans attendre d'appel AJAX
        element.textContent = compteur;

        // Toujours afficher le badge (même à 0 pour les utilisateurs anonymes)
        element.style.display = '';
        element.classList.remove('d-none');
    });
}

/**
 * Met à jour le compteur des favoris dans la navbar via AJAX (pour les mises à jour dynamiques)
 */
function mettreAJourCompteurFavoris() {
    fetch('/favoris/compteur', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        }
        throw new Error('Erreur lors de la récupération du compteur');
    })
    .then(compteur => {
        console.log('🔄 Mise à jour compteur favoris:', compteur);

        // Mettre à jour tous les compteurs de favoris
        const compteurs = document.querySelectorAll('.favorites-counter');
        compteurs.forEach(element => {
            element.textContent = compteur;
            // Toujours afficher le badge (même à 0 pour les utilisateurs anonymes)
            element.style.display = '';
            element.classList.remove('d-none');

            // Ajouter une animation quand le compteur change
            element.classList.add('updated');
            setTimeout(() => {
                element.classList.remove('updated');
            }, 600);
        });
    })
    .catch(error => {
        console.error('❌ Erreur mise à jour compteur favoris:', error);
    });
}

// Fonction chargerContenuFavoris() définie plus bas dans le fichier



/**
 * Ajoute un produit aux favoris (fonction globale pour les autres pages)
 */
function ajouterAuxFavorisGlobal(produitId) {
    console.log('💖 Ajout aux favoris - Produit ID:', produitId);

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
    };
    
    const csrfToken = getCsrfTokenFavoris();
    const csrfHeader = getCsrfHeaderFavoris();
    if (csrfToken && csrfHeader) {
        headers[csrfHeader] = csrfToken;
    }

    fetch(`/favoris/ajouter/${produitId}`, {
        method: 'POST',
        headers: headers
    })
    .then(response => response.text())
    .then(data => {
        if (data === 'success') {
            console.log('✅ Produit ajouté aux favoris');
            
            // Mettre à jour le compteur
            mettreAJourCompteurFavoris();
            
            // Afficher une notification
            if (typeof TechShop !== 'undefined' && TechShop.showNotification) {
                TechShop.showNotification('Produit ajouté aux favoris !', 'success');
            }
            
            // Mettre à jour l'icône du bouton favori si elle existe
            mettreAJourIconeFavori(produitId, true);
        } else {
            console.warn('⚠️ Réponse serveur favoris:', data);
            if (typeof TechShop !== 'undefined' && TechShop.showNotification) {
                TechShop.showNotification(data, 'warning');
            }
        }
    })
    .catch(error => {
        console.error('❌ Erreur ajout aux favoris:', error);
        if (typeof TechShop !== 'undefined' && TechShop.showNotification) {
            TechShop.showNotification('Erreur lors de l\'ajout aux favoris', 'error');
        }
    });
}



/**
 * Met à jour l'icône du bouton favori sur la page
 */
function mettreAJourIconeFavori(produitId, estFavori) {
    const boutonsFavoris = document.querySelectorAll(`[data-produit-id="${produitId}"].btn-favori`);
    boutonsFavoris.forEach(bouton => {
        const icone = bouton.querySelector('i');
        if (icone) {
            if (estFavori) {
                icone.classList.remove('far');
                icone.classList.add('fas');
                bouton.classList.remove('btn-outline-danger');
                bouton.classList.add('btn-danger');
                bouton.title = 'Retirer des favoris';
            } else {
                icone.classList.remove('fas');
                icone.classList.add('far');
                bouton.classList.remove('btn-danger');
                bouton.classList.add('btn-outline-danger');
                bouton.title = 'Ajouter aux favoris';
            }
        }
    });
}

// ==================== INITIALISATION ====================

/**
 * Initialise le système de favoris au chargement de la page
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 Initialisation système favoris');

    // Initialiser immédiatement le compteur avec les données serveur
    initialiserCompteurFavorisImmediat();

    // Attacher les événements aux dropdowns favoris après un délai réduit
    setTimeout(() => {
        initFavorisDropdown();
    }, 500);
});

// Initialisation immédiate du compteur (sans attendre DOMContentLoaded)
function initCompteurFavorisImmediat() {
    // Vérifier si les éléments sont déjà disponibles
    const compteurs = document.querySelectorAll('.favorites-counter');
    if (compteurs.length > 0) {
        // Utiliser d'abord l'initialisation immédiate avec les données serveur
        initialiserCompteurFavorisImmediat();
    } else {
        // Si pas encore disponibles, réessayer dans 50ms
        setTimeout(initCompteurFavorisImmediat, 50);
    }
}

// Initialisation immédiate du compteur favoris
initCompteurFavorisImmediat();

/**
 * Initialise les dropdowns favoris
 */
function initFavorisDropdown() {
    console.log('💖 Initialisation dropdown favoris');
    
    // Attacher les événements aux dropdowns favoris
    const dropdownTriggers = document.querySelectorAll('#favorisDropdownClient, #favorisDropdownAnonyme');
    console.log('🎯 Dropdowns favoris trouvés:', dropdownTriggers.length);
    
    dropdownTriggers.forEach((trigger, index) => {
        console.log(`🔧 Configuration dropdown favoris ${index + 1}:`, trigger.id);
        
        // Supprimer les anciens événements pour éviter les doublons
        trigger.removeEventListener('show.bs.dropdown', handleFavorisDropdownShow);
        trigger.removeEventListener('click', handleFavorisDropdownClick);
        
        // Ajouter les nouveaux événements
        trigger.addEventListener('show.bs.dropdown', handleFavorisDropdownShow);
        trigger.addEventListener('click', handleFavorisDropdownClick);
        
        console.log(`✅ Événements attachés au dropdown favoris ${index + 1}`);
    });
}

function handleFavorisDropdownShow(event) {
    console.log('💖 Dropdown favoris ouvert, chargement du contenu...');
    // Ne pas empêcher l'ouverture automatique, laisser Bootstrap gérer

    // Charger le contenu des favoris
    chargerContenuFavoris().catch(error => {
        console.error('❌ Erreur chargement favoris:', error);
    });
}

function handleFavorisDropdownClick(event) {
    console.log('🖱️ Clic sur dropdown favoris');
    // Laisser Bootstrap gérer l'ouverture/fermeture

    // Charger le contenu si le dropdown va s'ouvrir
    const dropdownMenu = event.target.closest('.dropdown').querySelector('.dropdown-menu');
    if (dropdownMenu && !dropdownMenu.classList.contains('show')) {
        chargerContenuFavoris().catch(error => {
            console.error('❌ Erreur chargement favoris:', error);
        });
    }
}

/**
 * Charge le contenu des favoris depuis le serveur
 */
function chargerContenuFavoris() {
    console.log('🔄 Chargement du contenu des favoris...');

    return fetch('/favoris/contenu', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('🔍 Réponse favoris:', {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('💖 Contenu favoris reçu:', data);
        afficherContenuFavoris(data);
        return data;
    })
    .catch(error => {
        console.error('❌ Erreur chargement contenu favoris:', error);
        afficherErreurFavoris();
        throw error;
    });
}

/**
 * Affiche le contenu des favoris dans le dropdown
 */
function afficherContenuFavoris(data) {
    const favorisContenu = document.getElementById('favoris-contenu');

    if (!favorisContenu) {
        console.error('❌ Élément favoris-contenu non trouvé');
        return;
    }

    console.log('🔍 Data reçue pour favoris:', data);

    // L'API retourne un objet avec {favoris: [...], nombreProduits: X, vide: boolean}
    const favorisArray = data.favoris || data || [];
    const nombreFavoris = data.nombreProduits || favorisArray.length || 0;
    const estVide = data.vide !== undefined ? data.vide : (favorisArray.length === 0);


    // Mettre à jour le compteur dans le header
    const favorisCompteurText = document.querySelector('.favoris-compteur-text');
    if (favorisCompteurText) {
        favorisCompteurText.textContent = `${nombreFavoris} produit${nombreFavoris > 1 ? 's' : ''}`;
    }


    if (estVide || !favorisArray || favorisArray.length === 0) {
        // Favoris vides
        favorisContenu.innerHTML = `
            <li class="px-3 py-2 text-center text-muted favoris-vide">
                <i class="fas fa-heart fa-2x mb-2"></i>
                <p class="mb-0">Aucun produit en favoris</p>
                <small>Ajoutez des produits à vos favoris</small>
            </li>
        `;
    } else {
        // Favoris avec des produits
        let contenuHTML = '';

        favorisArray.forEach(produit => {
            const imageUrl = produit.urlImage || '/images/default-product.jpg';
            const prix = produit.prix || 0;

            contenuHTML += `
                <li class="px-3 py-2 border-bottom ligne-favori" data-produit-id="${produit.id}">
                    <div class="d-flex align-items-center">
                        <img src="${imageUrl}"
                             alt="${produit.designation || produit.nom || 'Produit'}"
                             class="rounded me-2"
                             style="width: 40px; height: 40px; object-fit: cover;"
                             onerror="this.src='/images/default-product.jpg'">
                        <div class="flex-grow-1">
                            <h6 class="mb-0 text-truncate" style="max-width: 180px;" title="${produit.designation || produit.nom || 'Produit'}">
                                ${produit.designation || produit.nom || 'Produit'}
                            </h6>
                            <small class="text-muted">
                                ${prix.toFixed(2)} €
                            </small>
                        </div>
                        <div class="text-end">
                            <a href="/produits/${produit.id}" class="btn btn-outline-primary btn-sm me-1" title="Voir">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button class="btn btn-outline-danger btn-sm supprimer-favori"
                                    data-produit-id="${produit.id}"
                                    title="Supprimer des favoris">
                                <i class="fas fa-heart-broken"></i>
                            </button>
                        </div>
                    </div>
                </li>
            `;
        });

        favorisContenu.innerHTML = contenuHTML;

        // Ajouter les événements pour supprimer des favoris
        ajouterEvenementsSuppressionFavoris();
    }
}

/**
 * Affiche une erreur dans le dropdown favoris
 */
function afficherErreurFavoris() {
    const favorisContenu = document.getElementById('favoris-contenu');
    if (favorisContenu) {
        favorisContenu.innerHTML = `
            <li class="px-3 py-2 text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p class="mb-0">Erreur de chargement</p>
                <small>Impossible de charger les favoris</small>
            </li>
        `;
    }
}

/**
 * Ajoute les événements de suppression des favoris
 */
function ajouterEvenementsSuppressionFavoris() {
    const boutonsSupprimer = document.querySelectorAll('.supprimer-favori');
    boutonsSupprimer.forEach(bouton => {
        bouton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const produitId = this.getAttribute('data-produit-id');
            supprimerDesFavoris(produitId);
        });
    });
}

/**
 * Supprime un produit des favoris
 */
function supprimerDesFavoris(produitId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce produit de vos favoris ?')) {
        return;
    }

    // Récupérer le token CSRF
    const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
    const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');

    // Préparer les headers avec CSRF
    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
    };
    if (csrfToken && csrfHeader) {
        headers[csrfHeader] = csrfToken;
    }

    fetch(`/favoris/supprimer/${produitId}`, {
        method: 'POST',
        headers: headers
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('success')) {
            console.log('✅ Produit supprimé des favoris');

            // Recharger le contenu des favoris
            chargerContenuFavoris();

            // Mettre à jour le compteur
            mettreAJourCompteurFavoris();

            // Afficher une notification si disponible
            if (typeof TechShop !== 'undefined' && TechShop.showNotification) {
                TechShop.showNotification('Produit supprimé des favoris', 'success');
            }
        } else {
            throw new Error('Erreur lors de la suppression');
        }
    })
    .catch(error => {
        console.error('❌ Erreur suppression favori:', error);
        if (typeof TechShop !== 'undefined' && TechShop.showNotification) {
            TechShop.showNotification('Erreur lors de la suppression', 'error');
        }
    });
}

// Fermer le dropdown quand on clique ailleurs
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('.favoris-dropdown.show');
    dropdowns.forEach(dropdown => {
        if (!dropdown.closest('.dropdown').contains(event.target)) {
            dropdown.classList.remove('show');
        }
    });
});
