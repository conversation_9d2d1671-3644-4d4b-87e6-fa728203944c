/* ==================== NAVBAR RESPONSIVE GAPS ==================== */

/* Assurer que la navbar ne déborde pas */
.navbar-nav {
    flex-wrap: nowrap;
    overflow: visible;
}

.navbar-collapse {
    flex-wrap: nowrap;
}

/* Éviter le débordement sur petits écrans */
@media (max-width: 991.98px) {
    .navbar-nav {
        flex-wrap: wrap;
    }
}

/* Gaps responsives pour la navbar */
.navbar-nav.gap-1.gap-md-2.gap-lg-3 {
    /* Mobile (xs) - gap minimal pour éviter le débordement */
    gap: 0.25rem !important;
}

/* Tablette (md) et plus */
@media (min-width: 768px) {
    .navbar-nav.gap-1.gap-md-2.gap-lg-3 {
        gap: 0.5rem !important;
    }
}

/* Desktop (lg) et plus */
@media (min-width: 992px) {
    .navbar-nav.gap-1.gap-md-2.gap-lg-3 {
        gap: 0.75rem !important;
    }
}

/* Extra large screens (xl) et plus */
@media (min-width: 1200px) {
    .navbar-nav.gap-1.gap-md-2.gap-lg-3 {
        gap: 1rem !important;
    }
}

/* ==================== GAPS SPÉCIFIQUES POUR COMPOSANTS ==================== */

/* Gap entre la barre de recherche et les boutons de droite */
.search-form {
    margin-right: 0.5rem;
}

@media (min-width: 768px) {
    .search-form {
        margin-right: 0.75rem;
    }
}

@media (min-width: 992px) {
    .search-form {
        margin-right: 1rem;
    }
}

/* ==================== RESPONSIVE NAVBAR COLLAPSE ==================== */

/* Amélioration de l'espacement dans le menu mobile */
@media (max-width: 991.98px) {
    .navbar-collapse {
        padding-top: 1rem;
    }
    
    .navbar-nav {
        gap: 0.75rem !important;
    }
    
    .navbar-nav .nav-item {
        margin-bottom: 0.5rem;
    }
    
    /* Séparateurs visuels dans le menu mobile */
    .navbar-nav .nav-item:not(:last-child) {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-bottom: 0.5rem;
    }
    
    /* Espacement spécial pour les boutons d'authentification */
    .connexion-item,
    .inscription-item {
        margin-top: 0.5rem;
    }
    
    /* Espacement pour les dropdowns */
    .favoris-item,
    .panier-item,
    .notifications-item {
        margin-bottom: 0.75rem;
    }
}

/* ==================== AMÉLIORATION DES DROPDOWNS ==================== */

/* Espacement uniforme pour les dropdowns */
.dropdown-menu {
    margin-top: 0.5rem;
}

/* Amélioration responsive des dropdowns */
@media (max-width: 767.98px) {
    .dropdown-menu {
        width: 100% !important;
        max-width: 300px;
        margin-top: 0.25rem;
    }
}

/* ==================== ALIGNEMENT VERTICAL RESPONSIVE ==================== */

/* Assurer l'alignement vertical sur tous les écrans */
.navbar-nav {
    align-items: center;
}

.navbar-nav .nav-item {
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

/* ==================== ESPACEMENT DES BADGES ==================== */

/* Amélioration de l'espacement des badges de compteur */
.position-relative .badge {
    top: -0.5rem;
    right: -0.5rem;
}

@media (max-width: 767.98px) {
    .position-relative .badge {
        top: -0.25rem;
        right: -0.25rem;
        font-size: 0.7rem;
    }
}

/* ==================== TRANSITIONS SMOOTH ==================== */

/* Transitions fluides pour les gaps */
.navbar-nav,
.navbar-nav .nav-item,
.navbar-nav .nav-link {
    transition: all 0.3s ease;
}

/* ==================== AMÉLIORATION MOBILE FIRST ==================== */

/* Optimisation pour très petits écrans */
@media (max-width: 575.98px) {
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .navbar-nav .nav-link {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }
    
    .search-form .form-control {
        font-size: 0.9rem;
    }
    
    .btn {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }
}

/* ==================== ESPACEMENT POUR ÉCRANS LARGES ==================== */

/* Optimisation pour très grands écrans */
@media (min-width: 1400px) {
    .navbar-nav.gap-1.gap-md-2.gap-lg-3 {
        gap: 1.25rem !important;
    }

    .search-form {
        margin-right: 1.25rem;
    }
}
