package com.techshop.ecommerce.controller.admin;

import com.techshop.ecommerce.entity.Categorie;
import com.techshop.ecommerce.entity.Commande;
import com.techshop.ecommerce.entity.LigneCommande;
import com.techshop.ecommerce.entity.Produit;
import com.techshop.ecommerce.entity.StatutCommande;
import com.techshop.ecommerce.entity.Utilisateur;
import com.techshop.ecommerce.enums.Role;
import com.techshop.ecommerce.service.*;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private ProduitService produitService;

    @Autowired
    private CategorieService categorieService;

    @Autowired
    private UtilisateurService utilisateurService;

    @Autowired
    private CommandeService commandeService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // ==================== DASHBOARD ====================

    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        System.out.println("=== DASHBOARD ADMIN APPELÉ ===");
        try {
            // Statistiques de base
            int totalProduits = produitService.findAll().size();
            int totalCategories = categorieService.findAll().size();
            int totalUtilisateurs = utilisateurService.findAll().size();
            int totalCommandes = commandeService.findAll().size();

            System.out.println("📊 Statistiques de base:");
            System.out.println("   - Total Produits: " + totalProduits);
            System.out.println("   - Total Catégories: " + totalCategories);
            System.out.println("   - Total Utilisateurs: " + totalUtilisateurs);
            System.out.println("   - Total Commandes: " + totalCommandes);

            model.addAttribute("totalProduits", totalProduits);
            model.addAttribute("totalCategories", totalCategories);
            model.addAttribute("totalUtilisateurs", totalUtilisateurs);
            model.addAttribute("totalCommandes", totalCommandes);

            // Statistiques avancées avec gestion d'erreur
            try {
                int produitsEnStock = produitService.findProduitsEnStock().size();
                int produitsEnRupture = produitService.findProduitsEnRupture().size();
                System.out.println("📦 Statistiques produits:");
                System.out.println("   - Produits en stock: " + produitsEnStock);
                System.out.println("   - Produits en rupture: " + produitsEnRupture);
                model.addAttribute("produitsEnStock", produitsEnStock);
                model.addAttribute("produitsEnRupture", produitsEnRupture);
            } catch (Exception e) {
                System.out.println("❌ Erreur lors du calcul des statistiques produits: " + e.getMessage());
                model.addAttribute("produitsEnStock", 0);
                model.addAttribute("produitsEnRupture", 0);
            }

            try {
                Long totalClients = utilisateurService.countByRole(Role.CLIENT);
                Long totalAdmins = utilisateurService.countByRole(Role.ADMIN);
                System.out.println("👥 Statistiques utilisateurs:");
                System.out.println("   - Total clients: " + totalClients);
                System.out.println("   - Total admins: " + totalAdmins);
                model.addAttribute("totalClients", totalClients);
                model.addAttribute("totalAdmins", totalAdmins);
            } catch (Exception e) {
                System.out.println("❌ Erreur lors du calcul des statistiques utilisateurs: " + e.getMessage());
                model.addAttribute("totalClients", 0L);
                model.addAttribute("totalAdmins", 0L);
            }

            // Chiffre d'affaires (calcul réel basé sur les commandes qui génèrent des revenus)
            try {
                var toutesLesCommandes = commandeService.findAll();

                // Calculer le chiffre d'affaires sur les commandes qui génèrent des revenus
                double chiffreAffairesReel = toutesLesCommandes.stream()
                    .filter(c -> c.getStatutCommande() == StatutCommande.LIVREE ||
                               c.getStatutCommande() == StatutCommande.CONFIRMEE ||
                               c.getStatutCommande() == StatutCommande.EXPEDIEE)
                    .mapToDouble(c -> c.getCoutCommande())
                    .sum();

                // Calculer aussi le chiffre d'affaires seulement des commandes livrées
                double chiffreAffairesLivrees = toutesLesCommandes.stream()
                    .filter(c -> c.getStatutCommande() == StatutCommande.LIVREE)
                    .mapToDouble(c -> c.getCoutCommande())
                    .sum();

                // Si pas de commandes génératrices de revenus, utiliser une simulation
                double chiffreAffaires = chiffreAffairesReel > 0 ? chiffreAffairesReel : 15750.50;
                boolean isSimulation = chiffreAffairesReel == 0;

                System.out.println("💰 DASHBOARD - Chiffre d'affaires détaillé:");
                System.out.println("   - CA (Confirmées + Expédiées + Livrées): " + String.format("%.2f", chiffreAffairesReel) + " €");
                System.out.println("   - CA (Seulement livrées): " + String.format("%.2f", chiffreAffairesLivrees) + " €");
                System.out.println("   - Montant affiché: " + String.format("%.2f", chiffreAffaires) + " €");
                System.out.println("   - Mode simulation: " + isSimulation);

                model.addAttribute("chiffreAffaires", chiffreAffaires);
                model.addAttribute("chiffreAffairesSimulation", isSimulation);
                model.addAttribute("chiffreAffairesLivrees", chiffreAffairesLivrees);
            } catch (Exception e) {
                System.out.println("❌ DASHBOARD - Erreur calcul chiffre d'affaires: " + e.getMessage());
                model.addAttribute("chiffreAffaires", 15750.50);
                model.addAttribute("chiffreAffairesSimulation", true);
                model.addAttribute("chiffreAffairesLivrees", 0.0);
            }

            try {
                model.addAttribute("commandesDuJour", commandeService.countCommandesDuJour());
            } catch (Exception e) {
                model.addAttribute("commandesDuJour", 0L);
            }

            // Activité récente avec gestion d'erreur
            try {
                model.addAttribute("commandesRecentes", commandeService.findCommandesRecentes(5));
            } catch (Exception e) {
                model.addAttribute("commandesRecentes", new ArrayList<>());
            }

            try {
                model.addAttribute("nouveauxUtilisateurs", utilisateurService.findNouveauxUtilisateurs(5));
            } catch (Exception e) {
                model.addAttribute("nouveauxUtilisateurs", new ArrayList<>());
            }

            try {
                model.addAttribute("produitsAjoutesRecemment", produitService.findProduitsRecents(5));
            } catch (Exception e) {
                model.addAttribute("produitsAjoutesRecemment", new ArrayList<>());
            }

            // Alertes avec gestion d'erreur
            try {
                var alertesStock = produitService.findProduitsStockFaible(10);
                System.out.println("🚨 DASHBOARD - Alertes stock:");
                System.out.println("   - Produits avec stock faible (< 10): " + alertesStock.size());
                if (!alertesStock.isEmpty()) {
                    alertesStock.forEach(p -> System.out.println("     * " + p.getDesignation() + " (stock: " + p.getQteStock() + ")"));
                }
                model.addAttribute("alertesStock", alertesStock);
            } catch (Exception e) {
                System.out.println("❌ DASHBOARD - Erreur alertes stock: " + e.getMessage());
                model.addAttribute("alertesStock", new ArrayList<>());
            }

            try {
                model.addAttribute("commandesEnAttente", commandeService.countCommandesEnAttente());
            } catch (Exception e) {
                model.addAttribute("commandesEnAttente", 0L);
            }

        } catch (Exception e) {
            // En cas d'erreur générale, on met des valeurs par défaut
            model.addAttribute("totalProduits", 0);
            model.addAttribute("totalCategories", 0);
            model.addAttribute("totalUtilisateurs", 0);
            model.addAttribute("totalCommandes", 0);
            model.addAttribute("error", "Erreur lors du chargement des données : " + e.getMessage());
        }

        return "admin/dashboard";
    }

    // ==================== TEST DASHBOARD ====================

    @GetMapping("/test-dashboard")
    public String testDashboard(Model model) {
        System.out.println("=== TEST DASHBOARD ADMIN (SANS SÉCURITÉ) ===");
        try {
            // Statistiques de base
            int totalProduits = produitService.findAll().size();
            int totalCategories = categorieService.findAll().size();
            int totalUtilisateurs = utilisateurService.findAll().size();
            int totalCommandes = commandeService.findAll().size();

            System.out.println("📊 TEST - Statistiques de base:");
            System.out.println("   - Total Produits: " + totalProduits);
            System.out.println("   - Total Catégories: " + totalCategories);
            System.out.println("   - Total Utilisateurs: " + totalUtilisateurs);
            System.out.println("   - Total Commandes: " + totalCommandes);

            model.addAttribute("totalProduits", totalProduits);
            model.addAttribute("totalCategories", totalCategories);
            model.addAttribute("totalUtilisateurs", totalUtilisateurs);
            model.addAttribute("totalCommandes", totalCommandes);

            // Statistiques avancées
            try {
                int produitsEnStock = produitService.findProduitsEnStock().size();
                int produitsEnRupture = produitService.findProduitsEnRupture().size();
                System.out.println("📦 TEST - Statistiques produits:");
                System.out.println("   - Produits en stock: " + produitsEnStock);
                System.out.println("   - Produits en rupture: " + produitsEnRupture);
                model.addAttribute("produitsEnStock", produitsEnStock);
                model.addAttribute("produitsEnRupture", produitsEnRupture);
            } catch (Exception e) {
                System.out.println("❌ TEST - Erreur statistiques produits: " + e.getMessage());
                model.addAttribute("produitsEnStock", 0);
                model.addAttribute("produitsEnRupture", 0);
            }

            try {
                Long totalClients = utilisateurService.countByRole(Role.CLIENT);
                Long totalAdmins = utilisateurService.countByRole(Role.ADMIN);
                System.out.println("👥 TEST - Statistiques utilisateurs:");
                System.out.println("   - Total clients: " + totalClients);
                System.out.println("   - Total admins: " + totalAdmins);
                model.addAttribute("totalClients", totalClients);
                model.addAttribute("totalAdmins", totalAdmins);
            } catch (Exception e) {
                System.out.println("❌ TEST - Erreur statistiques utilisateurs: " + e.getMessage());
                model.addAttribute("totalClients", 0L);
                model.addAttribute("totalAdmins", 0L);
            }

            // Chiffre d'affaires
            model.addAttribute("chiffreAffaires", 15750.50);

            try {
                Long commandesDuJour = commandeService.countCommandesDuJour();
                System.out.println("📅 TEST - Commandes du jour: " + commandesDuJour);
                model.addAttribute("commandesDuJour", commandesDuJour);
            } catch (Exception e) {
                System.out.println("❌ TEST - Erreur commandes du jour: " + e.getMessage());
                model.addAttribute("commandesDuJour", 0L);
            }

            try {
                Long commandesEnAttente = commandeService.countCommandesEnAttente();
                System.out.println("⏳ TEST - Commandes en attente: " + commandesEnAttente);
                model.addAttribute("commandesEnAttente", commandesEnAttente);
            } catch (Exception e) {
                System.out.println("❌ TEST - Erreur commandes en attente: " + e.getMessage());
                model.addAttribute("commandesEnAttente", 0L);
            }

            // Données par défaut pour les listes
            model.addAttribute("commandesRecentes", new ArrayList<>());
            model.addAttribute("nouveauxUtilisateurs", new ArrayList<>());
            model.addAttribute("produitsAjoutesRecemment", new ArrayList<>());
            model.addAttribute("alertesStock", new ArrayList<>());

            System.out.println("✅ TEST - Dashboard chargé avec succès");

        } catch (Exception e) {
            System.out.println("❌ TEST - Erreur générale dashboard: " + e.getMessage());
            e.printStackTrace();
            // Valeurs par défaut en cas d'erreur
            model.addAttribute("totalProduits", 0);
            model.addAttribute("totalCategories", 0);
            model.addAttribute("totalUtilisateurs", 0);
            model.addAttribute("totalCommandes", 0);
            model.addAttribute("error", "Erreur lors du chargement des données : " + e.getMessage());
        }

        return "admin/dashboard";
    }

    // ==================== LOGOUT ====================

    @GetMapping("/logout")
    public String logout(HttpServletRequest request, HttpServletResponse response, Authentication auth) {
        if (auth != null) {
            new SecurityContextLogoutHandler().logout(request, response, auth);
        }
        return "redirect:/";
    }

    // ==================== PRODUITS ====================

    @GetMapping("/produits")
    public String produits(@RequestParam(required = false) String search,
                          @RequestParam(required = false) Long categorie,
                          @RequestParam(required = false) String stock,
                          Model model) {
        try {
            // Récupérer tous les produits avec leurs catégories pour éviter LazyInitializationException
            var tousLesProduits = produitService.findAllWithCategorie();

            // Appliquer les filtres
            var produits = tousLesProduits;

            // Filtre par recherche (nom ou référence)
            if (search != null && !search.trim().isEmpty()) {
                produits = produits.stream()
                    .filter(p -> p.getDesignation().toLowerCase().contains(search.toLowerCase()) ||
                               p.getReference().toLowerCase().contains(search.toLowerCase()) ||
                               (p.getMarque() != null && p.getMarque().toLowerCase().contains(search.toLowerCase())))
                    .toList();
                model.addAttribute("search", search);
            }

            // Filtre par catégorie
            if (categorie != null) {
                produits = produits.stream()
                    .filter(p -> p.getCategorie() != null && p.getCategorie().getId().equals(categorie))
                    .toList();
                model.addAttribute("selectedCategorie", categorie);
            }

            // Filtre par stock
            if (stock != null && !stock.trim().isEmpty()) {
                if ("en_stock".equals(stock)) {
                    produits = produits.stream()
                        .filter(p -> p.getQteStock() > 0)
                        .toList();
                } else if ("rupture".equals(stock)) {
                    produits = produits.stream()
                        .filter(p -> p.getQteStock() == 0)
                        .toList();
                }
                model.addAttribute("selectedStock", stock);
            }

            model.addAttribute("produits", produits);

            // Calculer les statistiques sur tous les produits
            long produitsEnStock = tousLesProduits.stream()
                .filter(p -> p.getQteStock() > 0)
                .count();

            long produitsRupture = tousLesProduits.stream()
                .filter(p -> p.getQteStock() == 0)
                .count();

            // Calculer les produits en stock faible (seuil : 5 unités)
            long produitsStockFaible = tousLesProduits.stream()
                .filter(p -> p.getQteStock() > 0 && p.getQteStock() <= 5)
                .count();

            // Ajouter les statistiques au modèle
            model.addAttribute("produitsEnStock", produitsEnStock);
            model.addAttribute("produitsRupture", produitsRupture);
            model.addAttribute("produitsStockFaible", produitsStockFaible);

            // Ajouter les catégories pour le filtre
            model.addAttribute("categories", categorieService.findAll());

        } catch (Exception e) {
            model.addAttribute("error", "Erreur lors du chargement des produits : " + e.getMessage());
            model.addAttribute("produits", new ArrayList<>());
            model.addAttribute("categories", new ArrayList<>());
            model.addAttribute("produitsEnStock", 0L);
            model.addAttribute("produitsRupture", 0L);
            model.addAttribute("produitsStockFaible", 0L);
        }

        return "admin/produits/list";
    }

    @GetMapping("/produits/nouveau")
    public String nouveauProduit(Model model) {
        model.addAttribute("produit", new Produit());
        model.addAttribute("categories", categorieService.findAll());
        return "admin/produits/form";
    }

    @PostMapping("/produits")
    public String creerProduit(@Valid @ModelAttribute Produit produit,
                              BindingResult result,
                              @RequestParam("categorie") Long categorieId,
                              Model model,
                              RedirectAttributes redirectAttributes) {

        // Gérer la conversion de l'ID de catégorie en objet Categorie
        if (categorieId != null) {
            Optional<Categorie> categorieOpt = categorieService.findById(categorieId);
            if (categorieOpt.isPresent()) {
                produit.setCategorie(categorieOpt.get());
            } else {
                result.rejectValue("categorie", "error.categorie", "Catégorie non trouvée");
            }
        } else {
            result.rejectValue("categorie", "error.categorie", "Veuillez sélectionner une catégorie");
        }

        if (result.hasErrors()) {
            model.addAttribute("categories", categorieService.findAll());
            return "admin/produits/form";
        }

        try {
            produitService.save(produit);
            redirectAttributes.addFlashAttribute("success", "Produit créé avec succès !");
            return "redirect:/admin/produits";
        } catch (Exception e) {
            model.addAttribute("error", "Erreur lors de la création : " + e.getMessage());
            model.addAttribute("categories", categorieService.findAll());
            return "admin/produits/form";
        }
    }

    @GetMapping("/produits/{id}")
    public String detailProduit(@PathVariable Long id, Model model) {
        Optional<Produit> produitOpt = produitService.findByIdWithCategorie(id);
        if (produitOpt.isEmpty()) {
            return "redirect:/admin/produits?error=produit-non-trouve";
        }
        model.addAttribute("produit", produitOpt.get());
        return "admin/produits/detail";
    }

    @GetMapping("/produits/{id}/modifier")
    public String modifierProduit(@PathVariable Long id, Model model) {
        Optional<Produit> produitOpt = produitService.findById(id);
        if (produitOpt.isEmpty()) {
            return "redirect:/admin/produits?error=produit-non-trouve";
        }
        model.addAttribute("produit", produitOpt.get());
        model.addAttribute("categories", categorieService.findAll());
        return "admin/produits/form";
    }

    @PostMapping("/produits/{id}")
    public String updateProduit(@PathVariable Long id,
                               @Valid @ModelAttribute Produit produit,
                               BindingResult result,
                               @RequestParam("categorie") Long categorieId,
                               Model model,
                               RedirectAttributes redirectAttributes) {

        // Gérer la conversion de l'ID de catégorie en objet Categorie
        if (categorieId != null) {
            Optional<Categorie> categorieOpt = categorieService.findById(categorieId);
            if (categorieOpt.isPresent()) {
                produit.setCategorie(categorieOpt.get());
            } else {
                result.rejectValue("categorie", "error.categorie", "Catégorie non trouvée");
            }
        } else {
            result.rejectValue("categorie", "error.categorie", "Veuillez sélectionner une catégorie");
        }

        if (result.hasErrors()) {
            model.addAttribute("categories", categorieService.findAll());
            return "admin/produits/form";
        }

        try {
            produit.setId(id);
            produitService.save(produit);
            redirectAttributes.addFlashAttribute("success", "Produit modifié avec succès !");
            return "redirect:/admin/produits";
        } catch (Exception e) {
            model.addAttribute("error", "Erreur lors de la modification : " + e.getMessage());
            model.addAttribute("categories", categorieService.findAll());
            return "admin/produits/form";
        }
    }

    @PostMapping("/produits/{id}/supprimer")
    public String supprimerProduit(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            produitService.deleteById(id);
            redirectAttributes.addFlashAttribute("success", "Produit supprimé avec succès !");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Erreur lors de la suppression : " + e.getMessage());
        }
        return "redirect:/admin/produits";
    }



    // ==================== UTILISATEURS ====================

    @GetMapping("/utilisateurs")
    public String utilisateurs(@RequestParam(required = false) String search,
                              @RequestParam(required = false) String role,
                              @RequestParam(required = false) String statut,
                              Model model) {
        try {
            // Récupérer tous les utilisateurs pour les statistiques
            var tousUtilisateurs = utilisateurService.findAll();

            // Si aucun paramètre de recherche, afficher tous les utilisateurs
            if ((search == null || search.trim().isEmpty()) &&
                (role == null || role.trim().isEmpty()) &&
                (statut == null || statut.trim().isEmpty())) {
                model.addAttribute("utilisateurs", tousUtilisateurs);
            } else {
                // Appliquer les filtres
                model.addAttribute("utilisateurs", utilisateurService.rechercherUtilisateurs(search, role, statut));
            }

            // Calculer les statistiques
            long totalClients = tousUtilisateurs.stream()
                .filter(u -> u.getCredential() != null && u.getCredential().getRole() == Role.CLIENT)
                .count();

            long comptesActifs = tousUtilisateurs.stream()
                .filter(u -> u.getCredential() != null && u.getCredential().getActif() == true)
                .count();

            long comptesInactifs = tousUtilisateurs.stream()
                .filter(u -> u.getCredential() != null && u.getCredential().getActif() == false)
                .count();

            // Ajouter les statistiques au modèle
            model.addAttribute("totalClients", totalClients);
            model.addAttribute("comptesActifs", comptesActifs);
            model.addAttribute("comptesInactifs", comptesInactifs);

            // Conserver les paramètres de recherche pour l'affichage
            model.addAttribute("searchParam", search);
            model.addAttribute("roleParam", role);
            model.addAttribute("statutParam", statut);

        } catch (Exception e) {
            model.addAttribute("error", "Erreur lors de la recherche : " + e.getMessage());
            model.addAttribute("utilisateurs", utilisateurService.findAll());
            // Valeurs par défaut en cas d'erreur
            model.addAttribute("totalClients", 0L);
            model.addAttribute("comptesActifs", 0L);
            model.addAttribute("comptesInactifs", 0L);
        }

        return "admin/utilisateurs/list";
    }

    @GetMapping("/utilisateurs/nouveau")
    public String nouvelUtilisateur(Model model) {
        model.addAttribute("utilisateur", new Utilisateur());
        model.addAttribute("roles", Role.values());
        return "admin/utilisateurs/form";
    }

    @PostMapping("/utilisateurs")
    public String creerUtilisateur(@Valid @ModelAttribute Utilisateur utilisateur,
                                  BindingResult result,
                                  @RequestParam String login,
                                  @RequestParam String motDePasse,
                                  @RequestParam Role role,
                                  Model model,
                                  RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            model.addAttribute("roles", Role.values());
            return "admin/utilisateurs/form";
        }

        try {
            utilisateurService.creerUtilisateur(utilisateur, login, motDePasse, role);
            redirectAttributes.addFlashAttribute("success", "Utilisateur créé avec succès !");
            return "redirect:/admin/utilisateurs";
        } catch (Exception e) {
            model.addAttribute("error", "Erreur lors de la création : " + e.getMessage());
            model.addAttribute("roles", Role.values());
            return "admin/utilisateurs/form";
        }
    }

    @GetMapping("/utilisateurs/{id}")
    public String detailUtilisateur(@PathVariable Long id, Model model) {
        Optional<Utilisateur> utilisateurOpt = utilisateurService.findById(id);
        if (utilisateurOpt.isEmpty()) {
            return "redirect:/admin/utilisateurs?error=utilisateur-non-trouve";
        }
        model.addAttribute("utilisateur", utilisateurOpt.get());
        return "admin/utilisateurs/detail";
    }

    @GetMapping("/utilisateurs/{id}/modifier")
    public String modifierUtilisateur(@PathVariable Long id, Model model) {
        Optional<Utilisateur> utilisateurOpt = utilisateurService.findById(id);
        if (utilisateurOpt.isEmpty()) {
            return "redirect:/admin/utilisateurs?error=utilisateur-non-trouve";
        }

        Utilisateur utilisateur = utilisateurOpt.get();
        model.addAttribute("utilisateur", utilisateur);
        model.addAttribute("roles", Role.values());

        // Essayer de récupérer les credentials par email
        try {
            Optional<Utilisateur> utilisateurAvecCredential = utilisateurService.findByEmailWithCredential(utilisateur.getEmail());
            if (utilisateurAvecCredential.isPresent() && utilisateurAvecCredential.get().getCredential() != null) {
                model.addAttribute("currentLogin", utilisateurAvecCredential.get().getCredential().getLogin());
                model.addAttribute("currentRole", utilisateurAvecCredential.get().getCredential().getRole());
                model.addAttribute("currentActif", utilisateurAvecCredential.get().getCredential().getActif());
            }
        } catch (Exception e) {
            // Si erreur, on continue sans les credentials
            System.out.println("Erreur lors de la récupération des credentials : " + e.getMessage());
        }

        return "admin/utilisateurs/form";
    }

    @PostMapping("/utilisateurs/{id}")
    public String updateUtilisateur(@PathVariable Long id,
                                   @Valid @ModelAttribute Utilisateur utilisateur,
                                   BindingResult result,
                                   @RequestParam(required = false) String login,
                                   @RequestParam(required = false) String motDePasse,
                                   @RequestParam(required = false) Role role,
                                   @RequestParam(required = false, defaultValue = "false") Boolean actif,
                                   Model model,
                                   RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            model.addAttribute("roles", Role.values());
            return "admin/utilisateurs/form";
        }

        try {
            // Récupérer l'utilisateur existant avec ses credentials
            Optional<Utilisateur> utilisateurExistantOpt = utilisateurService.findById(id);
            if (utilisateurExistantOpt.isEmpty()) {
                model.addAttribute("error", "Utilisateur non trouvé.");
                model.addAttribute("roles", Role.values());
                return "admin/utilisateurs/form";
            }

            Utilisateur utilisateurExistant = utilisateurExistantOpt.get();

            // Mettre à jour les informations personnelles
            utilisateurExistant.setPrenom(utilisateur.getPrenom());
            utilisateurExistant.setNom(utilisateur.getNom());
            utilisateurExistant.setEmail(utilisateur.getEmail());
            utilisateurExistant.setTelephone(utilisateur.getTelephone());

            // Sauvegarder l'utilisateur
            utilisateurService.updateUtilisateur(utilisateurExistant);

            // Mettre à jour les credentials si ils existent
            if (utilisateurExistant.getCredential() != null) {
                if (login != null && !login.trim().isEmpty()) {
                    utilisateurExistant.getCredential().setLogin(login);
                }

                if (motDePasse != null && !motDePasse.trim().isEmpty()) {
                    utilisateurExistant.getCredential().setMotDePasse(passwordEncoder.encode(motDePasse));
                }

                if (role != null) {
                    utilisateurExistant.getCredential().setRole(role);
                }

                // Mettre à jour le statut actif
                utilisateurExistant.getCredential().setActif(actif);

                // Sauvegarder les credentials
                utilisateurService.updateCredential(utilisateurExistant.getCredential());
            }

            redirectAttributes.addFlashAttribute("success", "Utilisateur modifié avec succès !");
            return "redirect:/admin/utilisateurs";
        } catch (Exception e) {
            model.addAttribute("error", "Erreur lors de la modification : " + e.getMessage());
            model.addAttribute("roles", Role.values());
            return "admin/utilisateurs/form";
        }
    }

    @PostMapping("/utilisateurs/{id}/statut")
    public String basculerStatutUtilisateur(@PathVariable Long id,
                                           @RequestParam(required = false) String returnTo,
                                           RedirectAttributes redirectAttributes) {
        try {
            Optional<Utilisateur> utilisateurOpt = utilisateurService.findById(id);
            if (utilisateurOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Utilisateur non trouvé.");
                return "redirect:/admin/utilisateurs";
            }

            Utilisateur utilisateur = utilisateurOpt.get();
            if (utilisateur.getCredential() == null) {
                redirectAttributes.addFlashAttribute("error", "Cet utilisateur n'a pas de compte d'accès.");
                return "redirect:/admin/utilisateurs";
            }

            // Basculer le statut
            boolean nouveauStatut = !utilisateur.getCredential().getActif();
            utilisateur.getCredential().setActif(nouveauStatut);

            // Sauvegarder les credentials
            utilisateurService.updateCredential(utilisateur.getCredential());

            String message = nouveauStatut ? "Compte activé avec succès !" : "Compte désactivé avec succès !";
            redirectAttributes.addFlashAttribute("success", message);

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Erreur lors de la modification du statut : " + e.getMessage());
        }

        // Rediriger vers la page de détail si la requête vient de la page de détail
        if ("detail".equals(returnTo)) {
            return "redirect:/admin/utilisateurs/" + id;
        }

        return "redirect:/admin/utilisateurs";
    }

    @PostMapping("/utilisateurs/{id}/supprimer")
    public String supprimerUtilisateur(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            Optional<Utilisateur> utilisateurOpt = utilisateurService.findById(id);
            if (utilisateurOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Utilisateur non trouvé");
                return "redirect:/admin/utilisateurs";
            }

            Utilisateur utilisateur = utilisateurOpt.get();

            // Vérifier s'il y a des commandes associées
            // TODO: Ajouter la vérification des commandes si nécessaire

            utilisateurService.deleteById(id);
            redirectAttributes.addFlashAttribute("success",
                "Utilisateur '" + utilisateur.getNomComplet() + "' supprimé avec succès");

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Erreur lors de la suppression : " + e.getMessage());
        }

        return "redirect:/admin/utilisateurs";
    }

    // ==================== COMMANDES ====================

    @GetMapping("/commandes")
    public String commandes(@RequestParam(required = false) String q,
                           @RequestParam(required = false) String statut,
                           @RequestParam(required = false) String dateDebut,
                           @RequestParam(required = false) String dateFin,
                           Model model) {
        try {
            // Récupérer toutes les commandes
            var toutesLesCommandes = commandeService.findAll();
            var commandes = toutesLesCommandes;

            // Appliquer les filtres
            // Filtre par recherche (référence ou nom du client)
            if (q != null && !q.trim().isEmpty()) {
                commandes = commandes.stream()
                    .filter(c -> c.getReferenceCommande().toLowerCase().contains(q.toLowerCase()) ||
                               (c.getUtilisateur().getNom() + " " + c.getUtilisateur().getPrenom()).toLowerCase().contains(q.toLowerCase()) ||
                               c.getUtilisateur().getEmail().toLowerCase().contains(q.toLowerCase()))
                    .toList();
            }

            // Filtre par statut
            if (statut != null && !statut.trim().isEmpty()) {
                try {
                    StatutCommande statutEnum = StatutCommande.valueOf(statut);
                    commandes = commandes.stream()
                        .filter(c -> c.getStatutCommande() == statutEnum)
                        .toList();
                } catch (IllegalArgumentException e) {
                    // Statut invalide, ignorer le filtre
                }
            }

            // Filtre par date de début
            if (dateDebut != null && !dateDebut.trim().isEmpty()) {
                try {
                    var dateDebutParsed = java.time.LocalDate.parse(dateDebut).atStartOfDay();
                    commandes = commandes.stream()
                        .filter(c -> c.getDateCommande().isAfter(dateDebutParsed) || c.getDateCommande().isEqual(dateDebutParsed))
                        .toList();
                } catch (Exception e) {
                    // Date invalide, ignorer le filtre
                }
            }

            // Filtre par date de fin
            if (dateFin != null && !dateFin.trim().isEmpty()) {
                try {
                    var dateFinParsed = java.time.LocalDate.parse(dateFin).atTime(23, 59, 59);
                    commandes = commandes.stream()
                        .filter(c -> c.getDateCommande().isBefore(dateFinParsed) || c.getDateCommande().isEqual(dateFinParsed))
                        .toList();
                } catch (Exception e) {
                    // Date invalide, ignorer le filtre
                }
            }

            model.addAttribute("commandes", commandes);

            // Calculer les statistiques réelles
            long commandesLivrees = commandeService.countCommandesLivrees();
            long commandesEnAttente = commandeService.countCommandesEnAttenteParStatut();
            long commandesConfirmees = commandeService.countCommandesConfirmees();
            long commandesExpediees = commandeService.countCommandesExpediees();
            long commandesAnnulees = commandeService.countCommandesAnnulees();

            // Calculer le chiffre d'affaires (seulement les commandes qui génèrent des revenus)
            double chiffreAffaires = commandes.stream()
                .filter(c -> c.getStatutCommande() == StatutCommande.LIVREE ||
                           c.getStatutCommande() == StatutCommande.CONFIRMEE ||
                           c.getStatutCommande() == StatutCommande.EXPEDIEE)
                .mapToDouble(c -> c.getCoutCommande())
                .sum();

            // Calculer aussi le chiffre d'affaires total (toutes commandes) pour comparaison
            double chiffreAffairesTotal = commandes.stream()
                .mapToDouble(c -> c.getCoutCommande())
                .sum();

            // Calculer le chiffre d'affaires seulement des commandes livrées
            double chiffreAffairesLivrees = commandes.stream()
                .filter(c -> c.getStatutCommande() == StatutCommande.LIVREE)
                .mapToDouble(c -> c.getCoutCommande())
                .sum();

            // Ajouter les statistiques au modèle
            model.addAttribute("commandesLivrees", commandesLivrees);
            model.addAttribute("commandesEnAttente", commandesEnAttente);
            model.addAttribute("commandesConfirmees", commandesConfirmees);
            model.addAttribute("commandesExpediees", commandesExpediees);
            model.addAttribute("commandesAnnulees", commandesAnnulees);
            model.addAttribute("chiffreAffaires", chiffreAffaires);
            model.addAttribute("chiffreAffairesTotal", chiffreAffairesTotal);
            model.addAttribute("chiffreAffairesLivrees", chiffreAffairesLivrees);

            // Logs pour debugging
            System.out.println("📊 COMMANDES - Statistiques calculées:");
            System.out.println("   - Total commandes (toutes): " + toutesLesCommandes.size());
            System.out.println("   - Commandes affichées (après filtres): " + commandes.size());
            System.out.println("   - Commandes en attente: " + commandesEnAttente);
            System.out.println("   - Commandes confirmées: " + commandesConfirmees);
            System.out.println("   - Commandes expédiées: " + commandesExpediees);
            System.out.println("   - Commandes livrées: " + commandesLivrees);
            System.out.println("   - Commandes annulées: " + commandesAnnulees);

            System.out.println("💰 COMMANDES - Chiffre d'affaires détaillé:");
            System.out.println("   - CA (Confirmées + Expédiées + Livrées): " + String.format("%.2f", chiffreAffaires) + " €");
            System.out.println("   - CA (Seulement livrées): " + String.format("%.2f", chiffreAffairesLivrees) + " €");
            System.out.println("   - CA (Toutes commandes): " + String.format("%.2f", chiffreAffairesTotal) + " €");

            // Logs des filtres appliqués
            if (q != null || statut != null || dateDebut != null || dateFin != null) {
                System.out.println("🔍 COMMANDES - Filtres appliqués:");
                if (q != null && !q.trim().isEmpty()) {
                    System.out.println("   - Recherche: '" + q + "'");
                }
                if (statut != null && !statut.trim().isEmpty()) {
                    System.out.println("   - Statut: '" + statut + "'");
                }
                if (dateDebut != null && !dateDebut.trim().isEmpty()) {
                    System.out.println("   - Date début: '" + dateDebut + "'");
                }
                if (dateFin != null && !dateFin.trim().isEmpty()) {
                    System.out.println("   - Date fin: '" + dateFin + "'");
                }
            }

        } catch (Exception e) {
            System.out.println("❌ COMMANDES - Erreur: " + e.getMessage());
            e.printStackTrace();
            model.addAttribute("error", "Erreur lors du chargement des commandes : " + e.getMessage());
            model.addAttribute("commandes", new ArrayList<>());
            model.addAttribute("commandesLivrees", 0L);
            model.addAttribute("commandesEnAttente", 0L);
            model.addAttribute("commandesConfirmees", 0L);
            model.addAttribute("commandesExpediees", 0L);
            model.addAttribute("commandesAnnulees", 0L);
            model.addAttribute("chiffreAffaires", 0.0);
            model.addAttribute("chiffreAffairesTotal", 0.0);
            model.addAttribute("chiffreAffairesLivrees", 0.0);
        }

        return "admin/commandes/list";
    }

    @GetMapping("/commandes/{id}")
    public String detailCommande(@PathVariable Long id, Model model) {
        Optional<Commande> commandeOpt = commandeService.findById(id);
        if (commandeOpt.isEmpty()) {
            return "redirect:/admin/commandes?error=commande-non-trouvee";
        }

        Commande commande = commandeOpt.get();
        List<LigneCommande> lignesCommande = commandeService.getLignesCommande(id);

        model.addAttribute("commande", commande);
        model.addAttribute("lignesCommande", lignesCommande);
        return "admin/commandes/detail";
    }

    @PostMapping("/commandes/{id}/statut")
    public String modifierStatutCommande(@PathVariable Long id,
                                       @RequestParam String statut,
                                       RedirectAttributes redirectAttributes) {
        try {
            Optional<Commande> commandeOpt = commandeService.findById(id);
            if (commandeOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Commande non trouvée");
                return "redirect:/admin/commandes";
            }

            Commande commande = commandeOpt.get();

            // Convertir le statut string en enum
            try {
                StatutCommande nouveauStatut = StatutCommande.valueOf(statut);
                commande.setStatutCommande(nouveauStatut);
                commandeService.save(commande);

                redirectAttributes.addFlashAttribute("success",
                    "Statut de la commande modifié avec succès vers : " + nouveauStatut.getLibelle());
            } catch (IllegalArgumentException e) {
                redirectAttributes.addFlashAttribute("error", "Statut invalide : " + statut);
            }

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error",
                "Erreur lors de la modification du statut : " + e.getMessage());
        }

        return "redirect:/admin/commandes/" + id;
    }

    @PostMapping("/commandes/{id}/supprimer")
    public String supprimerCommande(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            Optional<Commande> commandeOpt = commandeService.findById(id);
            if (commandeOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Commande non trouvée");
                return "redirect:/admin/commandes";
            }

            Commande commande = commandeOpt.get();
            commandeService.deleteById(id);
            redirectAttributes.addFlashAttribute("success",
                "Commande '" + commande.getReferenceCommande() + "' supprimée avec succès");

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error",
                "Erreur lors de la suppression : " + e.getMessage());
        }

        return "redirect:/admin/commandes";
    }

    // ==================== TEST COMMANDES STATS ====================

    @GetMapping("/test-commandes-stats")
    @ResponseBody
    public String testCommandesStats() {
        System.out.println("=== TEST COMMANDES STATS (PUBLIC) ===");

        try {
            // Test des nouvelles statistiques de commandes
            long commandesEnAttente = commandeService.countCommandesEnAttenteParStatut();
            long commandesConfirmees = commandeService.countCommandesConfirmees();
            long commandesExpediees = commandeService.countCommandesExpediees();
            long commandesLivrees = commandeService.countCommandesLivrees();
            long commandesAnnulees = commandeService.countCommandesAnnulees();

            System.out.println("📊 TEST PUBLIC - Statistiques commandes par statut:");
            System.out.println("   - Commandes en attente: " + commandesEnAttente);
            System.out.println("   - Commandes confirmées: " + commandesConfirmees);
            System.out.println("   - Commandes expédiées: " + commandesExpediees);
            System.out.println("   - Commandes livrées: " + commandesLivrees);
            System.out.println("   - Commandes annulées: " + commandesAnnulees);

            System.out.println("✅ TEST PUBLIC - Statistiques commandes calculées avec succès");

            return "Test des statistiques commandes réussi ! Vérifiez les logs de la console.";

        } catch (Exception e) {
            System.out.println("❌ TEST PUBLIC - Erreur statistiques commandes: " + e.getMessage());
            e.printStackTrace();
            return "Erreur lors du test des statistiques commandes : " + e.getMessage();
        }
    }
}
