package com.techshop.ecommerce.service;

import com.techshop.ecommerce.entity.Produit;
import com.techshop.ecommerce.exception.ResourceNotFoundException;
import com.techshop.ecommerce.exception.BusinessException;
import com.techshop.ecommerce.exception.StockInsuffisantException;
import com.techshop.ecommerce.repository.ProduitRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class ProduitService {

    private static final Logger logger = LoggerFactory.getLogger(ProduitService.class);

    @Autowired
    private ProduitRepository produitRepository;

    /**
     * Récupère tous les produits avec cache
     */
    @Cacheable(value = "produits", key = "'all'")
    @Transactional(readOnly = true)
    public List<Produit> findAll() {
        logger.debug("Récupération de tous les produits");
        return produitRepository.findAll();
    }

    /**
     * Récupère tous les produits avec leurs catégories (pour éviter LazyInitializationException)
     */
    @Cacheable(value = "produits", key = "'all-with-categories'")
    @Transactional(readOnly = true)
    public List<Produit> findAllWithCategorie() {
        logger.debug("Récupération de tous les produits avec leurs catégories");
        return produitRepository.findAllWithCategorie();
    }

    /**
     * Récupère un produit par ID avec cache
     */
    @Cacheable(value = "produits", key = "#id")
    @Transactional(readOnly = true)
    public Optional<Produit> findById(Long id) {
        if (id == null) {
            throw new BusinessException("L'ID du produit ne peut pas être null");
        }
        logger.debug("Récupération du produit avec ID: {}", id);
        return produitRepository.findById(id);
    }

    /**
     * Récupère un produit par ID ou lève une exception si non trouvé
     */
    @Cacheable(value = "produits", key = "#id")
    @Transactional(readOnly = true)
    public Produit findByIdOrThrow(Long id) {
        return findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Produit", "id", id));
    }



    /**
     * Récupère un produit par référence
     */
    @Cacheable(value = "produits", key = "'ref_' + #reference")
    @Transactional(readOnly = true)
    public Optional<Produit> findByReference(String reference) {
        if (!StringUtils.hasText(reference)) {
            throw new BusinessException("La référence du produit ne peut pas être vide");
        }
        logger.debug("Récupération du produit avec référence: {}", reference);
        return produitRepository.findByReference(reference);
    }

    public List<Produit> findByMarque(String marque) {
        return produitRepository.findByMarque(marque);
    }

    public List<Produit> findByCategorieId(Long categorieId) {
        return produitRepository.findByCategorieId(categorieId);
    }

    public List<Produit> findByCategorieIds(List<Long> categorieIds) {
        return produitRepository.findByCategorieIdIn(categorieIds);
    }

    /**
     * Récupère plusieurs produits par leurs IDs
     */
    @Transactional(readOnly = true)
    public List<Produit> findAllById(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        logger.debug("Récupération de {} produits par IDs", ids.size());
        return produitRepository.findAllById(ids);
    }

    public List<Produit> findProduitsEnStock() {
        return produitRepository.findByQteStockGreaterThan(0);
    }

    public Page<Produit> findByKeyword(String keyword, Pageable pageable) {
        return produitRepository.findByKeyword(keyword, pageable);
    }

    public List<Produit> findByPrixBetween(Double minPrix, Double maxPrix) {
        return produitRepository.findByPrixBetween(minPrix, maxPrix);
    }

    /**
     * Sauvegarde un produit avec invalidation du cache
     */
    @CachePut(value = "produits", key = "#produit.id", condition = "#produit.id != null")
    @CacheEvict(value = "produits", key = "'all'")
    public Produit save(Produit produit) {
        if (produit == null) {
            throw new BusinessException("Le produit ne peut pas être null");
        }

        // Validation métier
        validateProduit(produit);

        logger.info("Sauvegarde du produit: {} (ID: {})", produit.getDesignation(), produit.getId());
        return produitRepository.save(produit);
    }

    /**
     * Supprime un produit par ID avec invalidation du cache
     */
    @CacheEvict(value = "produits", allEntries = true)
    public void deleteById(Long id) {
        if (id == null) {
            throw new BusinessException("L'ID du produit ne peut pas être null");
        }

        // Vérifier que le produit existe
        if (!produitRepository.existsById(id)) {
            throw new ResourceNotFoundException("Produit", "id", id);
        }

        logger.info("Suppression du produit avec ID: {}", id);
        produitRepository.deleteById(id);
    }

    /**
     * Validation métier d'un produit
     */
    private void validateProduit(Produit produit) {
        if (!StringUtils.hasText(produit.getDesignation())) {
            throw new BusinessException("La désignation du produit est obligatoire");
        }

        if (produit.getPrix() == null || produit.getPrix() <= 0) {
            throw new BusinessException("Le prix du produit doit être positif");
        }

        if (produit.getQteStock() == null || produit.getQteStock() < 0) {
            throw new BusinessException("La quantité en stock ne peut pas être négative");
        }

        // Vérifier l'unicité de la référence si elle est fournie
        if (StringUtils.hasText(produit.getReference())) {
            Optional<Produit> existant = produitRepository.findByReference(produit.getReference());
            if (existant.isPresent() && !existant.get().getId().equals(produit.getId())) {
                throw new BusinessException("Un produit avec cette référence existe déjà: " + produit.getReference());
            }
        }
    }

    public Optional<Produit> findByIdWithCategorie(Long id) {
        return produitRepository.findByIdWithCategorie(id);
    }

    /**
     * Met à jour le stock d'un produit avec validation
     */
    @CachePut(value = "produits", key = "#produitId")
    public Produit updateStock(Long produitId, Integer nouvelleQuantite) {
        if (produitId == null) {
            throw new BusinessException("L'ID du produit ne peut pas être null");
        }

        if (nouvelleQuantite == null || nouvelleQuantite < 0) {
            throw new BusinessException("La nouvelle quantité doit être positive ou nulle");
        }

        Produit produit = findByIdOrThrow(produitId);
        Integer ancienStock = produit.getQteStock();

        produit.setQteStock(nouvelleQuantite);
        Produit produitSauvegarde = produitRepository.save(produit);

        logger.info("Stock mis à jour pour le produit {} (ID: {}): {} → {}",
                   produit.getDesignation(), produitId, ancienStock, nouvelleQuantite);

        return produitSauvegarde;
    }

    /**
     * Vérifie si un produit est en stock pour une quantité donnée
     */
    @Transactional(readOnly = true)
    public boolean isEnStock(Long produitId, Integer quantiteDemandee) {
        if (produitId == null || quantiteDemandee == null) {
            return false;
        }

        if (quantiteDemandee <= 0) {
            throw new BusinessException("La quantité demandée doit être positive");
        }

        Optional<Produit> produitOpt = findById(produitId);
        if (produitOpt.isPresent()) {
            Produit produit = produitOpt.get();
            boolean enStock = produit.getQteStock() >= quantiteDemandee;

            if (!enStock) {
                logger.debug("Stock insuffisant pour le produit {} (ID: {}): stock={}, demandé={}",
                           produit.getDesignation(), produitId, produit.getQteStock(), quantiteDemandee);
            }

            return enStock;
        }

        logger.warn("Tentative de vérification de stock pour un produit inexistant: {}", produitId);
        return false;
    }

    /**
     * Réserve du stock pour un produit (décrémente le stock)
     */
    @CachePut(value = "produits", key = "#produitId")
    public Produit reserverStock(Long produitId, Integer quantite) {
        if (produitId == null || quantite == null || quantite <= 0) {
            throw new BusinessException("Paramètres invalides pour la réservation de stock");
        }

        Produit produit = findByIdOrThrow(produitId);

        if (produit.getQteStock() < quantite) {
            throw new StockInsuffisantException(produitId, produit.getQteStock(), quantite);
        }

        Integer nouveauStock = produit.getQteStock() - quantite;
        produit.setQteStock(nouveauStock);

        Produit produitSauvegarde = produitRepository.save(produit);

        logger.info("Stock réservé pour le produit {} (ID: {}): -{} (nouveau stock: {})",
                   produit.getDesignation(), produitId, quantite, nouveauStock);

        return produitSauvegarde;
    }

    /**
     * Libère du stock pour un produit (incrémente le stock)
     */
    @CachePut(value = "produits", key = "#produitId")
    public Produit libererStock(Long produitId, Integer quantite) {
        if (produitId == null || quantite == null || quantite <= 0) {
            throw new BusinessException("Paramètres invalides pour la libération de stock");
        }

        Produit produit = findByIdOrThrow(produitId);
        Integer nouveauStock = produit.getQteStock() + quantite;
        produit.setQteStock(nouveauStock);

        Produit produitSauvegarde = produitRepository.save(produit);

        logger.info("Stock libéré pour le produit {} (ID: {}): +{} (nouveau stock: {})",
                   produit.getDesignation(), produitId, quantite, nouveauStock);

        return produitSauvegarde;
    }

    public List<Produit> findProduitsEnRupture() {
        return produitRepository.findByQteStock(0);
    }

    public List<Produit> findProduitsRecents(int limit) {
        return produitRepository.findTop5ByOrderByCreeLe();
    }

    public List<Produit> findProduitsStockFaible(int seuilStock) {
        return produitRepository.findByQteStockLessThanAndQteStockGreaterThan(seuilStock, 0);
    }

    public Page<Produit> rechercheAvancee(String nom, Long categorieId, Double prixMin, Double prixMax,
                                         String marque, Boolean enStock, Pageable pageable) {
        // Commencer avec tous les produits
        List<Produit> produits = produitRepository.findAll();

        // Filtrer par nom si spécifié
        if (nom != null && !nom.trim().isEmpty()) {
            String nomLower = nom.toLowerCase();
            produits = produits.stream()
                .filter(p -> p.getDesignation().toLowerCase().contains(nomLower))
                .collect(Collectors.toList());
        }

        // Filtrer par catégorie si spécifiée
        if (categorieId != null) {
            produits = produits.stream()
                .filter(p -> p.getCategorie() != null && p.getCategorie().getId().equals(categorieId))
                .collect(Collectors.toList());
        }

        // Filtrer par prix minimum
        if (prixMin != null) {
            produits = produits.stream()
                .filter(p -> p.getPrix() >= prixMin)
                .collect(Collectors.toList());
        }

        // Filtrer par prix maximum
        if (prixMax != null) {
            produits = produits.stream()
                .filter(p -> p.getPrix() <= prixMax)
                .collect(Collectors.toList());
        }

        // Filtrer par marque si spécifiée
        if (marque != null && !marque.trim().isEmpty()) {
            String marqueLower = marque.toLowerCase();
            produits = produits.stream()
                .filter(p -> p.getMarque() != null && p.getMarque().toLowerCase().contains(marqueLower))
                .collect(Collectors.toList());
        }

        // Filtrer par stock si spécifié
        if (enStock != null && enStock) {
            produits = produits.stream()
                .filter(p -> p.getQteStock() > 0)
                .collect(Collectors.toList());
        }

        // Appliquer le tri selon le Pageable
        if (pageable.getSort().isSorted()) {
            produits = produits.stream()
                .sorted((p1, p2) -> {
                    String sortProperty = pageable.getSort().iterator().next().getProperty();
                    boolean isAsc = pageable.getSort().iterator().next().isAscending();

                    int comparison = 0;
                    switch (sortProperty) {
                        case "nom":
                        case "designation":
                            comparison = p1.getDesignation().compareToIgnoreCase(p2.getDesignation());
                            break;
                        case "prix":
                            comparison = p1.getPrix().compareTo(p2.getPrix());
                            break;
                        case "creeLe":
                            comparison = p1.getCreeLe().compareTo(p2.getCreeLe());
                            break;
                        default:
                            comparison = p1.getId().compareTo(p2.getId());
                    }

                    return isAsc ? comparison : -comparison;
                })
                .collect(Collectors.toList());
        }

        // Calculer la pagination
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), produits.size());

        List<Produit> pageContent = produits.subList(start, end);

        return new PageImpl<>(pageContent, pageable, produits.size());
    }
}
