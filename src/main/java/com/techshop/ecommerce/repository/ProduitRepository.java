package com.techshop.ecommerce.repository;

import com.techshop.ecommerce.entity.Produit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProduitRepository extends JpaRepository<Produit, Long> {

    Optional<Produit> findByReference(String reference);

    List<Produit> findByMarque(String marque);

    List<Produit> findByCategorieId(Long categorieId);

    List<Produit> findByCategorieIdIn(List<Long> categorieIds);

    List<Produit> findByQteStockGreaterThan(Integer stock);

    @Query("SELECT p FROM Produit p WHERE p.designation LIKE %:keyword% OR p.marque LIKE %:keyword%")
    Page<Produit> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    @Query("SELECT p FROM Produit p WHERE p.prix BETWEEN :minPrix AND :maxPrix")
    List<Produit> findByPrixBetween(@Param("minPrix") Double minPrix, @Param("maxPrix") Double maxPrix);

    @Query("SELECT p FROM Produit p JOIN FETCH p.categorie WHERE p.id = :id")
    Optional<Produit> findByIdWithCategorie(@Param("id") Long id);

    @Query("SELECT p FROM Produit p JOIN FETCH p.categorie")
    List<Produit> findAllWithCategorie();

    // Méthodes pour le dashboard admin
    List<Produit> findByQteStock(Integer qteStock);

    List<Produit> findTop5ByOrderByCreeLe();

    List<Produit> findByQteStockLessThanAndQteStockGreaterThan(Integer maxStock, Integer minStock);
}
