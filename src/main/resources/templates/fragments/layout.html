<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:fragment="head">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title th:text="${title} ?: 'TechShop'">TechShop</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link th:href="@{/css/style.css}" rel="stylesheet">
    <!-- Navbar Unified CSS -->
    <link th:href="@{/css/navbar-unified.css}" rel="stylesheet">
    <!-- Navbar Responsive Gaps CSS -->
    <link th:href="@{/css/navbar-responsive-gaps.css}" rel="stylesheet">
</head>

<body class="page-wrapper">
    <!-- Navigation -->
    <nav th:fragment="navbar" class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-store"></i> TechShop
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse align-items-center" id="navbarNav">
                <ul class="navbar-nav me-auto align-items-center">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/}">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/produits}">Produits</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/categories}">Catégories</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/produits/recherche}">
                            <i class="fas fa-search"></i> Recherche Avancée
                        </a>
                    </li>
                    <!-- Bouton Dashboard Admin (visible uniquement pour les ADMIN) -->
                    <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                        <a class="nav-link btn btn-outline-light ms-2 px-3" th:href="@{/admin/dashboard}"
                           title="Accéder au Dashboard Administrateur">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                </ul>

                <!-- Recherche rapide au centre -->
                <form class="d-flex mx-auto search-form align-items-center" th:action="@{/produits/recherche}" method="get">
                    <input class="form-control" type="search" name="q" placeholder="Rechercher des produits..."
                           aria-label="Rechercher">
                    <button class="btn btn-outline-light" type="submit" title="Rechercher">
                        <i class="fas fa-search"></i>
                    </button>
                </form>

                <ul class="navbar-nav align-items-center gap-1 gap-md-2 gap-lg-3">
                    <!-- ==================== SECTION FAVORIS ==================== -->

                    <!-- Favoris pour utilisateurs CLIENT connectés -->
                    <li class="nav-item dropdown favoris-item" sec:authorize="hasRole('CLIENT')">
                        <a class="nav-link position-relative dropdown-toggle"
                           href="#"
                           id="favorisDropdownClient"
                           role="button"
                           data-bs-toggle="dropdown"
                           aria-expanded="false"
                           title="Voir vos produits favoris">
                            <i class="fas fa-heart"></i> Favoris
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger favorites-counter"
                                  th:text="${favorisCompteur ?: 0}"
                                  th:style="${(favorisCompteur ?: 0) == 0 ? 'display: none;' : ''}">
                                0
                            </span>
                        </a>

                        <!-- Dropdown menu pour les favoris CLIENT -->
                        <ul class="dropdown-menu dropdown-menu-end favoris-dropdown"
                            aria-labelledby="favorisDropdownClient">

                            <!-- Contenu des favoris (sera rempli dynamiquement) -->
                            <li class="dropdown-header">
                                <strong>Mes Favoris</strong>
                                <span class="float-end">
                                    <small class="text-muted favoris-compteur-text">0 produit(s)</small>
                                </span>
                            </li>
                            <li><hr class="dropdown-divider"></li>

                            <!-- Liste des favoris (sera remplie dynamiquement) -->
                            <li id="favoris-contenu" class="px-3 py-2 text-center text-muted favoris-vide">
                                <i class="fas fa-heart fa-2x mb-2"></i>
                                <p class="mb-0">Aucun produit en favoris</p>
                                <small>Ajoutez des produits à vos favoris</small>
                            </li>

                            <!-- Footer du dropdown -->
                            <li><hr class="dropdown-divider favoris-footer-divider" style="display: none;"></li>
                            <li class="px-3 py-2 favoris-footer" style="display: none;">
                                <div class="d-grid gap-2">
                                    <!-- Bouton pour utilisateurs connectés -->
                                    <a href="/client/favoris" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-heart"></i> Voir tous mes favoris
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </li>

                    <!-- Favoris pour utilisateurs ANONYMES -->
                    <li class="nav-item dropdown favoris-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link position-relative dropdown-toggle"
                           href="#"
                           id="favorisDropdownAnonyme"
                           role="button"
                           data-bs-toggle="dropdown"
                           aria-expanded="false"
                           title="Voir vos produits favoris">
                            <i class="fas fa-heart"></i> Favoris
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning favorites-counter"
                                  th:text="${favorisCompteur ?: 0}">
                                0
                            </span>
                        </a>

                        <!-- Dropdown menu pour les favoris ANONYMES -->
                        <ul class="dropdown-menu dropdown-menu-end favoris-dropdown"
                            aria-labelledby="favorisDropdownAnonyme">

                            <!-- Contenu des favoris (sera rempli dynamiquement) -->
                            <li class="dropdown-header">
                                <strong>Mes Favoris</strong>
                                <span class="float-end">
                                    <small class="text-muted favoris-compteur-text" th:text="${favorisCompteur ?: 0} + ' favori' + (${favorisCompteur ?: 0} > 1 ? 's' : '')">0 produit(s)</small>
                                </span>
                            </li>
                            <li><hr class="dropdown-divider"></li>

                            <!-- Liste des favoris (sera remplie dynamiquement) -->
                            <li id="favoris-contenu-anonyme" class="px-3 py-2 text-center text-muted favoris-vide">
                                <i class="fas fa-heart fa-2x mb-2"></i>
                                <p class="mb-0">Aucun produit en favoris</p>
                                <small>Ajoutez des produits à vos favoris</small>
                            </li>

                            <!-- Footer du dropdown -->
                            <li><hr class="dropdown-divider favoris-footer-divider" style="display: none;"></li>
                            <li class="px-3 py-2 favoris-footer" style="display: none;">
                                <div class="d-grid gap-2">
                                    <!-- Boutons pour utilisateurs anonymes -->
                                    <a href="/login" class="btn btn-primary btn-sm mb-1">
                                        <i class="fas fa-sign-in-alt"></i> Se connecter
                                    </a>
                                    <small class="text-muted d-block text-center">
                                        Connectez-vous pour sauvegarder vos favoris
                                    </small>
                                </div>
                            </li>
                        </ul>
                    </li>

                    <!-- ==================== SECTION PANIER ==================== -->

                    <li class="nav-item dropdown panier-item">
                        <!-- Panier pour utilisateurs CLIENT connectés -->
                        <a class="nav-link position-relative dropdown-toggle"
                           href="#"
                           id="panierDropdownClient"
                           role="button"
                           data-bs-toggle="dropdown"
                           aria-expanded="false"
                           sec:authorize="hasRole('CLIENT')"
                           title="Voir le contenu de votre panier">
                            <i class="fas fa-shopping-cart"></i> Panier
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger cart-counter"
                                  th:text="${panierCompteur ?: 0}">
                                0
                            </span>
                        </a>

                        <!-- Panier pour utilisateurs ADMIN connectés -->
                        <a class="nav-link position-relative" th:href="@{/admin/paniers}" sec:authorize="hasRole('ADMIN')"
                           title="Gestion des paniers">
                            <i class="fas fa-shopping-cart"></i> Paniers
                            <span class="badge bg-info ms-1">Admin</span>
                        </a>

                        <!-- Panier pour utilisateurs ANONYMES -->
                        <a class="nav-link position-relative dropdown-toggle"
                           href="#"
                           id="panierDropdownAnonyme"
                           role="button"
                           data-bs-toggle="dropdown"
                           aria-expanded="false"
                           sec:authorize="!isAuthenticated()"
                           title="Voir le contenu de votre panier">
                            <i class="fas fa-shopping-cart"></i> Panier
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning cart-counter"
                                  th:text="${panierCompteur ?: 0}">
                                0
                            </span>
                        </a>

                        <!-- Dropdown menu pour le panier (utilisateurs connectés et anonymes) -->
                        <ul class="dropdown-menu dropdown-menu-end panier-dropdown"
                            aria-labelledby="panierDropdownClient panierDropdownAnonyme">

                            <!-- Contenu du panier (sera rempli dynamiquement) -->
                            <li class="dropdown-header">
                                <strong>Mon Panier</strong>
                                <span class="float-end">
                                    <small class="text-muted panier-compteur-text">0 article(s)</small>
                                </span>
                            </li>
                            <li><hr class="dropdown-divider"></li>

                            <!-- Zone de contenu du panier -->
                            <div id="panier-contenu">
                                <!-- Panier vide -->
                                <li class="px-3 py-2 text-center text-muted panier-vide">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                    <p class="mb-0">Votre panier est vide</p>
                                    <small>Ajoutez des produits pour commencer vos achats</small>
                                </li>
                            </div>

                            <!-- Footer du dropdown -->
                            <li><hr class="dropdown-divider panier-footer-divider" style="display: none;"></li>
                            <li class="px-3 py-2 panier-footer" style="display: none;">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <strong>Total:</strong>
                                    <strong class="text-primary panier-total">0,00 €</strong>
                                </div>
                                <div class="d-grid gap-2">
                                    <!-- Bouton pour utilisateurs connectés -->
                                    <a href="/client/panier" class="btn btn-primary btn-sm" sec:authorize="hasRole('CLIENT')">
                                        <i class="fas fa-shopping-cart"></i> Voir le panier
                                    </a>
                                    <!-- Boutons pour utilisateurs anonymes -->
                                    <div sec:authorize="!isAuthenticated()">
                                        <a href="/login" class="btn btn-primary btn-sm mb-1">
                                            <i class="fas fa-sign-in-alt"></i> Se connecter
                                        </a>
                                        <small class="text-muted d-block text-center">
                                            Connectez-vous pour finaliser votre commande
                                        </small>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </li>

                    <!-- Menu pour utilisateur connecté -->
                    <li class="nav-item dropdown notifications-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="true">
                            <i class="fas fa-user"></i> <span sec:authentication="name">Utilisateur</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <!-- Informations utilisateur (toujours visible) -->
                            <li>
                                <h6 class="dropdown-header">
                                    <i class="fas fa-user"></i> <span sec:authentication="name">Utilisateur</span>
                                </h6>
                            </li>
                            <li><hr class="dropdown-divider"></li>

                            <!-- Options pour ADMIN -->
                            <li sec:authorize="hasRole('ADMIN')">
                                <a class="dropdown-item" th:href="@{/admin/dashboard}">
                                    <i class="fas fa-tachometer-alt"></i> Dashboard Admin
                                </a>
                            </li>
                            <li sec:authorize="hasRole('ADMIN')">
                                <a class="dropdown-item" th:href="@{/admin/produits}">
                                    <i class="fas fa-box"></i> Gestion Produits
                                </a>
                            </li>
                            <li sec:authorize="hasRole('ADMIN')">
                                <a class="dropdown-item" th:href="@{/admin/categories}">
                                    <i class="fas fa-tags"></i> Gestion Catégories
                                </a>
                            </li>
                            <li sec:authorize="hasRole('ADMIN')">
                                <a class="dropdown-item" th:href="@{/admin/utilisateurs}">
                                    <i class="fas fa-users"></i> Gestion Utilisateurs
                                </a>
                            </li>
                            <li sec:authorize="hasRole('ADMIN')">
                                <a class="dropdown-item" th:href="@{/admin/commandes}">
                                    <i class="fas fa-shopping-bag"></i> Gestion Commandes
                                </a>
                            </li>
                            <li sec:authorize="hasRole('ADMIN')">
                                <a class="dropdown-item" th:href="@{/admin/paniers}">
                                    <i class="fas fa-shopping-cart"></i> Gestion Paniers
                                </a>
                            </li>
                            <li sec:authorize="hasRole('ADMIN')">
                                <a class="dropdown-item" th:href="@{/admin/stock}">
                                    <i class="fas fa-warehouse"></i> Gestion Stock
                                </a>
                            </li>

                            <!-- Options pour CLIENT -->
                            <li sec:authorize="hasRole('CLIENT')">
                                <a class="dropdown-item" th:href="@{/client/dashboard}">
                                    <i class="fas fa-user-circle"></i> Mon Compte
                                </a>
                            </li>
                            <li sec:authorize="hasRole('CLIENT')">
                                <a class="dropdown-item" th:href="@{/client/profil}">
                                    <i class="fas fa-edit"></i> Modifier Profil
                                </a>
                            </li>
                            <li sec:authorize="hasRole('CLIENT')">
                                <a class="dropdown-item" th:href="@{/client/commandes}">
                                    <i class="fas fa-shopping-bag"></i> Mes Commandes
                                </a>
                            </li>
                            <li sec:authorize="hasRole('CLIENT')">
                                <a class="dropdown-item" th:href="@{/client/favoris}">
                                    <i class="fas fa-heart"></i> Mes Favoris
                                </a>
                            </li>

                            <!-- Options communes (pour tous les utilisateurs connectés) -->
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" th:href="@{/}">
                                    <i class="fas fa-home"></i> Accueil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/test-auth">
                                    <i class="fas fa-cog"></i> Test Authentification
                                </a>
                            </li>

                            <!-- Séparateur et déconnexion (toujours visible) -->
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline w-100">
                                    <button type="submit" class="dropdown-item border-0 bg-transparent w-100 text-start">
                                        <i class="fas fa-sign-out-alt"></i> Déconnexion
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>

                    <!-- ==================== SECTION AUTHENTIFICATION ANONYME ==================== -->

                    <!-- Boutons de connexion pour utilisateurs ANONYMES -->
                    <li class="nav-item connexion-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link btn btn-outline-light px-3 py-2" th:href="@{/login}"
                           title="Se connecter à votre compte">
                            <i class="fas fa-sign-in-alt"></i> Connexion
                        </a>
                    </li>
                    <li class="nav-item inscription-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link btn btn-primary px-3 py-2" th:href="@{/register}"
                           title="Créer un nouveau compte">
                            <i class="fas fa-user-plus"></i> Inscription
                        </a>
                    </li>


                    <!-- Notifications (visible pour les utilisateurs connectés) -->
                    <li class="nav-item dropdown" sec:authorize="isAuthenticated()">
                        <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill" style="background-color: #c2185b;">
                                3
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown">
                            <li>
                                <h6 class="dropdown-header">
                                    <i class="fas fa-bell"></i> Notifications
                                </h6>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-info-circle text-info"></i> Bienvenue sur TechShop !
                                </a>
                            </li>
                            <li sec:authorize="hasRole('CLIENT')">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-truck text-success"></i> Votre commande est en cours
                                </a>
                            </li>
                            <li sec:authorize="hasRole('ADMIN')">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> 5 nouveaux utilisateurs
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="#">
                                    <small>Voir toutes les notifications</small>
                                </a>
                            </li>
                        </ul>
                    </li>



                </ul>
            </div>
        </div>
    </nav>

    <!-- Admin Sidebar -->
    <div th:fragment="sidebar" class="col-md-3 col-lg-2">
        <div class="card fade-in" style="animation-delay: 0s;">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> Administration</h6>
            </div>
            <div class="list-group list-group-flush">
                <a th:href="@{/admin/dashboard}" class="list-group-item list-group-item-action">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a th:href="@{/admin/produits}" class="list-group-item list-group-item-action">
                    <i class="fas fa-box"></i> Produits
                </a>
                <a th:href="@{/admin/categories}" class="list-group-item list-group-item-action">
                    <i class="fas fa-tags"></i> Catégories
                </a>
                <a th:href="@{/admin/utilisateurs}" class="list-group-item list-group-item-action">
                    <i class="fas fa-users"></i> Utilisateurs
                </a>
                <a th:href="@{/admin/commandes}" class="list-group-item list-group-item-action">
                    <i class="fas fa-shopping-bag"></i> Commandes
                </a>
                <a th:href="@{/admin/paniers}" class="list-group-item list-group-item-action">
                    <i class="fas fa-shopping-cart"></i> Paniers
                </a>
                <a th:href="@{/admin/stock}" class="list-group-item list-group-item-action">
                    <i class="fas fa-warehouse"></i> Gestion Stock
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer th:fragment="footer" class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>TechShop</h5>
                    <p>Votre boutique en ligne de confiance pour tous vos besoins technologiques.</p>
                </div>
                <div class="col-md-6">
                    <h5>Liens utiles</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light">À propos</a></li>
                        <li><a href="#" class="text-light">Contact</a></li>
                        <li><a href="#" class="text-light">Conditions d'utilisation</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2025 TechShop. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Client Sidebar -->
    <div th:fragment="client-sidebar" class="col-md-3">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="fas fa-user-circle"></i> Mon Compte</h6>
            </div>
            <div class="list-group list-group-flush">
                <a th:href="@{/client/dashboard}"
                   class="list-group-item list-group-item-action">
                    <i class="fas fa-home"></i> Tableau de bord
                </a>
                <a th:href="@{/client/profil}"
                   class="list-group-item list-group-item-action">
                    <i class="fas fa-user-edit"></i> Mon Profil
                </a>
                <a th:href="@{/client/commandes}"
                   class="list-group-item list-group-item-action">
                    <i class="fas fa-shopping-bag"></i> Mes Commandes
                </a>
                <a th:href="@{/client/favoris}"
                   class="list-group-item list-group-item-action">
                    <i class="fas fa-heart"></i> Mes Favoris
                </a>
                <a th:href="@{/client/adresses}"
                   class="list-group-item list-group-item-action">
                    <i class="fas fa-map-marker-alt"></i> Mes Adresses
                </a>
                <a th:href="@{/}" class="list-group-item list-group-item-action text-muted">
                    <i class="fas fa-arrow-left"></i> Retour au site
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/techshop.js}"></script>
    <script th:src="@{/js/panier.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <!-- Dropdown Position Unified JS -->
    <script th:src="@{/js/dropdown-position-unified.js}"></script>

    <!-- Initialize Bootstrap Components -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS ===');

            // Check if Bootstrap is loaded
            if (typeof bootstrap !== 'undefined') {
                console.log('✅ Bootstrap is loaded, version:', bootstrap.Tooltip.VERSION || 'unknown');

                // Find all dropdown toggles
                const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles');

                // Initialize dropdowns manually if needed
                dropdownToggles.forEach((toggle, index) => {
                    console.log(`🎯 Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                    try {
                        // Create Bootstrap dropdown instance
                        const dropdown = new bootstrap.Dropdown(toggle);
                        console.log(`✅ Dropdown ${index + 1} initialized successfully`);

                        // Add event listeners for debugging
                        toggle.addEventListener('show.bs.dropdown', function() {
                            console.log(`📂 Dropdown ${index + 1} is opening`);
                        });

                        toggle.addEventListener('shown.bs.dropdown', function() {
                            console.log(`📂 Dropdown ${index + 1} is now open`);
                        });

                        toggle.addEventListener('hide.bs.dropdown', function() {
                            console.log(`📁 Dropdown ${index + 1} is closing`);
                        });

                    } catch (error) {
                        console.error(`❌ Error initializing dropdown ${index + 1}:`, error);
                    }
                });

            } else {
                console.error('❌ Bootstrap is not loaded!');

                // Try to load Bootstrap manually
                console.log('🔄 Attempting to load Bootstrap manually...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                script.onload = function() {
                    console.log('✅ Bootstrap loaded manually');
                    // Re-run initialization
                    setTimeout(() => location.reload(), 1000);
                };
                script.onerror = function() {
                    console.error('❌ Failed to load Bootstrap manually');
                };
                document.head.appendChild(script);
            }

            // Test click events manually
            const userDropdown = document.getElementById('navbarDropdown');
            if (userDropdown) {
                console.log('🎯 User dropdown found:', userDropdown);

                userDropdown.addEventListener('click', function(e) {
                    console.log('🖱️ User dropdown clicked', e);

                    // If Bootstrap dropdown doesn't work, toggle manually
                    if (typeof bootstrap === 'undefined') {
                        e.preventDefault();
                        const menu = userDropdown.nextElementSibling;
                        if (menu && menu.classList.contains('dropdown-menu')) {
                            menu.classList.toggle('show');
                            console.log('🔧 Manually toggled dropdown menu');
                        }
                    }
                });
            } else {
                console.log('❌ User dropdown not found');
            }
        });
    </script>
</body>
</html>
