<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Mon Panier - TechShop</title>
</head>
<body class="client-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <div class="client-main">
        <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Accueil</a></li>
                <li class="breadcrumb-item"><a th:href="@{/client/dashboard}">Mon Compte</a></li>
                <li class="breadcrumb-item active"><PERSON></li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-shopping-cart text-primary"></i> Mon Panier
                <span class="badge bg-secondary" th:text="${lignesPanier != null ? #lists.size(lignesPanier) : 0}">0</span>
            </h2>
            <a th:href="@{/produits}" class="btn btn-outline-primary">
                <i class="fas fa-plus"></i> Continuer mes achats
            </a>
        </div>

        <!-- Messages -->
        <div class="alert alert-success alert-dismissible fade show" th:if="${success}">
            <i class="fas fa-check-circle"></i> <span th:text="${success}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="alert alert-danger alert-dismissible fade show" th:if="${error}">
            <i class="fas fa-exclamation-triangle"></i> <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="row">
            <!-- Liste des articles -->
            <div class="col-md-8">
                <div th:if="${lignesPanier != null and !#lists.isEmpty(lignesPanier)}">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Articles dans votre panier</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Produit</th>
                                            <th>Prix unitaire</th>
                                            <th>Quantité</th>
                                            <th>Sous-total</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="ligne : ${lignesPanier}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img th:src="${ligne.produit.urlImage} ?: 'https://via.placeholder.com/80x60'"
                                                         class="img-thumbnail me-3" style="width: 80px; height: 60px; object-fit: cover;"
                                                         alt="Produit">
                                                    <div>
                                                        <h6 class="mb-1" th:text="${ligne.produit.designation}">Nom Produit</h6>
                                                        <small class="text-muted" th:text="${ligne.produit.marque}">Marque</small><br>
                                                        <small class="text-muted">Réf: <span th:text="${ligne.produit.reference}">REF001</span></small>
                                                        <div th:if="${ligne.produit.qteStock < ligne.quantite}" class="text-danger">
                                                            <small><i class="fas fa-exclamation-triangle"></i> Stock insuffisant</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold" th:text="${#numbers.formatDecimal(ligne.produit.prix, 0, 2)} + ' €'">Prix</span>
                                            </td>
                                            <td>
                                                <div class="input-group" style="width: 120px;">
                                                    <button class="btn btn-outline-secondary btn-sm" type="button"
                                                            onclick="modifierQuantite([[${ligne.panierId}]], [[${ligne.produitId}]], -1)">-</button>
                                                    <input type="number" class="form-control form-control-sm text-center"
                                                           th:value="${ligne.quantite}"
                                                           th:id="'qty-' + ${ligne.panierId} + '-' + ${ligne.produitId}"
                                                           min="1" th:max="${ligne.produit.qteStock}"
                                                           onchange="updateQuantite([[${ligne.panierId}]], [[${ligne.produitId}]], this.value)">
                                                    <button class="btn btn-outline-secondary btn-sm" type="button"
                                                            onclick="modifierQuantite([[${ligne.panierId}]], [[${ligne.produitId}]], 1)"
                                                            th:disabled="${ligne.quantite >= ligne.produit.qteStock}">+</button>
                                                </div>
                                                <small class="text-muted d-block mt-1">
                                                    Stock: <span th:text="${ligne.produit.qteStock}">10</span>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-primary"
                                                      th:text="${#numbers.formatDecimal(ligne.coutLigne, 0, 2)} + ' €'">Sous-total</span>
                                            </td>
                                            <td>
                                                <button class="btn btn-outline-danger btn-sm"
                                                        onclick="supprimerDuPanier([[${ligne.panierId}]], [[${ligne.produitId}]])"
                                                        title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Actions sur le panier -->
                    <div class="card mt-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-outline-warning" onclick="viderPanier()">
                                    <i class="fas fa-trash"></i> Vider le panier
                                </button>
                                <button class="btn btn-outline-secondary" onclick="sauvegarderPourPlusTard()">
                                    <i class="fas fa-bookmark"></i> Sauvegarder pour plus tard
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- État vide -->
                <div th:if="${lignesPanier == null or #lists.isEmpty(lignesPanier)}" class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">Votre panier est vide</h4>
                    <p class="text-muted">Découvrez nos produits et ajoutez-les à votre panier !</p>
                    <a th:href="@{/produits}" class="btn btn-primary">
                        <i class="fas fa-search"></i> Découvrir nos produits
                    </a>
                </div>
            </div>

            <!-- Résumé de commande -->
            <div class="col-md-4" th:if="${lignesPanier != null and !#lists.isEmpty(lignesPanier)}">
                <div class="card sticky-top" style="top: 20px;">
                    <div class="card-header">
                        <h5 class="mb-0">Résumé de la commande</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Sous-total:</span>
                            <span th:text="${#numbers.formatDecimal(total ?: 0, 0, 2)} + ' €'">0,00 €</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Livraison:</span>
                            <span class="text-success">Gratuite</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>TVA (20%):</span>
                            <span th:text="${#numbers.formatDecimal((total ?: 0) * 0.2, 0, 2)} + ' €'">0,00 €</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total:</strong>
                            <strong class="text-primary" th:text="${#numbers.formatDecimal((total ?: 0) * 1.2, 0, 2)} + ' €'">0,00 €</strong>
                        </div>

                        <!-- Code promo -->
                        <div class="mb-3">
                            <label for="codePromo" class="form-label">Code promo</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="codePromo" placeholder="Entrez votre code">
                                <button class="btn btn-outline-secondary" type="button">Appliquer</button>
                            </div>
                        </div>

                        <!-- Bouton commander -->
                        <form th:action="@{/client/commandes/passer}" method="post" th:if="${panier}">
                            <input type="hidden" name="panierId" th:value="${panier.id}">
                            <button type="submit" class="btn btn-primary w-100 mb-2"
                                    th:disabled="${lignesPanier.?[produit.qteStock < quantite].size() > 0}">
                                <i class="fas fa-credit-card"></i> Passer la commande
                            </button>
                        </form>

                        <div th:if="${lignesPanier.?[produit.qteStock < quantite].size() > 0}" class="alert alert-warning">
                            <small><i class="fas fa-exclamation-triangle"></i>
                            Certains articles ont un stock insuffisant.</small>
                        </div>

                        <!-- Moyens de paiement -->
                        <div class="text-center mt-3">
                            <small class="text-muted">Paiement sécurisé</small><br>
                            <i class="fab fa-cc-visa fa-2x text-muted me-2"></i>
                            <i class="fab fa-cc-mastercard fa-2x text-muted me-2"></i>
                            <i class="fab fa-paypal fa-2x text-muted"></i>
                        </div>
                    </div>
                </div>

                <!-- Produits recommandés -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Vous pourriez aussi aimer</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center text-muted">
                            <small>Recommandations basées sur votre panier</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div> <!-- End container -->
    </div> <!-- End client-main -->

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <script>
        // Debugging Bootstrap Dropdowns pour la page panier
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS (PAGE PANIER) ===');

            // Attendre que Bootstrap soit chargé
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap is loaded on panier page, version:', bootstrap.Tooltip.VERSION || 'unknown');

                    // Find all dropdown toggles
                    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                    console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles on panier page');

                    // Initialize dropdowns manually
                    dropdownToggles.forEach((toggle, index) => {
                        console.log(`🎯 Panier Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                        try {
                            // Create Bootstrap dropdown instance
                            const dropdown = new bootstrap.Dropdown(toggle);
                            console.log(`✅ Panier Dropdown ${index + 1} initialized successfully`);

                            // Add event listeners for debugging
                            toggle.addEventListener('show.bs.dropdown', function() {
                                console.log(`📂 Panier Dropdown ${index + 1} is opening`);
                            });

                            toggle.addEventListener('shown.bs.dropdown', function() {
                                console.log(`📂 Panier Dropdown ${index + 1} is now open`);
                            });

                            toggle.addEventListener('click', function(e) {
                                console.log(`🖱️ Panier Dropdown ${index + 1} clicked`, e);
                            });

                        } catch (error) {
                            console.error(`❌ Error initializing panier dropdown ${index + 1}:`, error);
                        }
                    });

                } else {
                    console.error('❌ Bootstrap is not loaded on panier page!');
                }
            }, 500); // Attendre 500ms pour que Bootstrap soit chargé

            // Initialiser le compteur du panier
            mettreAJourCompteurPanier();
        });

        // Fonction pour mettre à jour le compteur du panier dans la navbar
        function mettreAJourCompteurPanier() {
            const headers = {
                'X-Requested-With': 'XMLHttpRequest'
            };

            // Récupération du token CSRF
            const csrfToken = document.querySelector('meta[name="_csrf"]').getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]').getAttribute('content');
            headers[csrfHeader] = csrfToken;

            fetch('/client/panier/compteur', {
                method: 'GET',
                headers: headers
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error('Erreur lors de la récupération du compteur');
            })
            .then(compteur => {
                console.log('Compteur panier mis à jour:', compteur);

                // Mettre à jour le badge du compteur dans la navbar
                const badge = document.querySelector('.cart-counter');
                if (badge) {
                    badge.textContent = compteur;

                    // Afficher/masquer le badge selon la valeur
                    if (compteur > 0) {
                        badge.style.display = '';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Erreur lors de la mise à jour du compteur:', error);
            });
        }

        function modifierQuantite(panierId, produitId, delta) {
            const input = document.getElementById('qty-' + panierId + '-' + produitId);
            const currentQty = parseInt(input.value);
            const newQty = Math.max(1, currentQty + delta);
            const maxQty = parseInt(input.getAttribute('max'));

            if (newQty <= maxQty) {
                updateQuantite(panierId, produitId, newQty);
            }
        }

        function updateQuantite(panierId, produitId, quantite) {
            fetch('/client/panier/modifier/' + panierId + '/' + produitId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'quantite=' + quantite
            })
            .then(response => response.text())
            .then(data => {
                if (data === 'success') {
                    // Mettre à jour le compteur du panier
                    mettreAJourCompteurPanier();
                    // Recharger la page pour mettre à jour les totaux
                    location.reload();
                } else {
                    TechShop.showNotification('Erreur: ' + data, 'error');
                }
            })
            .catch(error => {
                TechShop.showNotification('Erreur de connexion', 'error');
            });
        }

        function supprimerDuPanier(panierId, produitId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cet article ?')) {
                fetch('/client/panier/supprimer/' + panierId + '/' + produitId, {
                    method: 'POST'
                })
                .then(response => response.text())
                .then(data => {
                    if (data === 'success') {
                        TechShop.showNotification('Article supprimé du panier', 'success');
                        // Mettre à jour le compteur du panier
                        mettreAJourCompteurPanier();
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        TechShop.showNotification('Erreur lors de la suppression', 'error');
                    }
                })
                .catch(error => {
                    TechShop.showNotification('Erreur de connexion', 'error');
                });
            }
        }

        function viderPanier() {
            if (confirm('Êtes-vous sûr de vouloir vider votre panier ?')) {
                // Implementation pour vider le panier
                TechShop.showNotification('Fonctionnalité en cours de développement', 'info');
            }
        }

        function sauvegarderPourPlusTard() {
            TechShop.showNotification('Panier sauvegardé pour plus tard', 'info');
        }
    </script>

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

</body>
</html>
