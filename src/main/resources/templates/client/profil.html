<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Mon Profil - TechShop</title>
</head>
<body class="client-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <div class="client-main">
        <div class="container mt-4">
            <div class="row">
                <!-- Sidebar Client -->
                <div th:replace="~{fragments/layout :: client-sidebar}"></div>

            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a th:href="@{/}">Accueil</a></li>
                        <li class="breadcrumb-item"><a th:href="@{/client/dashboard}">Mon <PERSON></a></li>
                        <li class="breadcrumb-item active">Mon Profil</li>
                    </ol>
                </nav>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-user-edit"></i> Mon Profil</h2>
                </div>

                <!-- Messages -->
                <div class="alert alert-success alert-dismissible fade show" th:if="${success}">
                    <i class="fas fa-check-circle"></i> <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <div class="alert alert-danger alert-dismissible fade show" th:if="${error}">
                    <i class="fas fa-exclamation-triangle"></i> <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Formulaire de profil -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Informations personnelles</h5>
                            </div>
                            <div class="card-body">
                                <form th:action="@{/client/profil}" method="post" th:object="${utilisateur}" class="needs-validation" novalidate>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="prenom" class="form-label">Prénom *</label>
                                                <input type="text" class="form-control" id="prenom" th:field="*{prenom}"
                                                       placeholder="Votre prénom" required>
                                                <div class="invalid-feedback">Veuillez saisir votre prénom.</div>
                                                <div class="text-danger" th:if="${#fields.hasErrors('prenom')}" th:errors="*{prenom}"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="nom" class="form-label">Nom *</label>
                                                <input type="text" class="form-control" id="nom" th:field="*{nom}"
                                                       placeholder="Votre nom" required>
                                                <div class="invalid-feedback">Veuillez saisir votre nom.</div>
                                                <div class="text-danger" th:if="${#fields.hasErrors('nom')}" th:errors="*{nom}"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="email" th:field="*{email}"
                                               placeholder="<EMAIL>" required unique>
                                        <div class="invalid-feedback">Veuillez saisir un email valide et unique.</div>
                                        <div class="text-danger" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="telephone" class="form-label">Téléphone</label>
                                        <input type="tel" class="form-control" id="telephone" th:field="*{telephone}"
                                               placeholder="0123456789" unique>
                                        <div class="invalid-feedback">Veuillez saisir un numéro de téléphone unique.</div>
                                        <div class="text-danger" th:if="${#fields.hasErrors('telephone')}" th:errors="*{telephone}"></div>
                                    </div>

                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            Compte créé le <span th:text="${#temporals.format(utilisateur.creeLe, 'dd/MM/yyyy à HH:mm')}">date</span>
                                        </small>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Enregistrer les modifications
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary">
                                            <i class="fas fa-undo"></i> Annuler
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Informations du compte -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Informations du compte</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Statut du compte:</strong><br>
                                    <span class="badge bg-success">Actif</span>
                                </div>

                                <div class="mb-3">
                                    <strong>Type de compte:</strong><br>
                                    <span class="badge bg-primary">Client</span>
                                </div>

                                <div class="mb-3">
                                    <strong>Dernière modification:</strong><br>
                                    <small class="text-muted" th:text="${#temporals.format(utilisateur.modifieLe, 'dd/MM/yyyy à HH:mm')}">date</small>
                                </div>

                                <hr>

                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-warning btn-sm" onclick="changerMotDePasse()">
                                        <i class="fas fa-key"></i> Changer le mot de passe
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="telechargerDonnees()">
                                        <i class="fas fa-download"></i> Télécharger mes données
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="supprimerCompte()">
                                        <i class="fas fa-user-times"></i> Supprimer le compte
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Statistiques -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Mes statistiques</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h4 class="text-primary mb-0">0</h4>
                                            <small class="text-muted">Commandes</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-danger mb-0">0</h4>
                                        <small class="text-muted">Favoris</small>
                                    </div>
                                </div>
                                <hr>
                                <div class="text-center">
                                    <h5 class="text-success mb-0">0,00 €</h5>
                                    <small class="text-muted">Total dépensé</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Préférences -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Préférences</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Notifications</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                    <label class="form-check-label" for="emailNotifications">
                                        Recevoir les notifications par email
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="promoNotifications" checked>
                                    <label class="form-check-label" for="promoNotifications">
                                        Recevoir les offres promotionnelles
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="orderNotifications" checked>
                                    <label class="form-check-label" for="orderNotifications">
                                        Notifications de commande
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Confidentialité</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="profilePublic">
                                    <label class="form-check-label" for="profilePublic">
                                        Profil public
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="shareData">
                                    <label class="form-check-label" for="shareData">
                                        Partager les données pour améliorer l'expérience
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary">
                                <i class="fas fa-save"></i> Sauvegarder les préférences
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            </div> <!-- End row -->
        </div> <!-- End container -->
    </div> <!-- End client-main -->

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <script>
        // Debugging Bootstrap Dropdowns pour la page profil
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS (PAGE PROFIL) ===');

            // Attendre que Bootstrap soit chargé
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap is loaded on profil page, version:', bootstrap.Tooltip.VERSION || 'unknown');

                    // Find all dropdown toggles
                    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                    console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles on profil page');

                    // Initialize dropdowns manually
                    dropdownToggles.forEach((toggle, index) => {
                        console.log(`🎯 Profil Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                        try {
                            // Create Bootstrap dropdown instance
                            const dropdown = new bootstrap.Dropdown(toggle);
                            console.log(`✅ Profil Dropdown ${index + 1} initialized successfully`);

                            // Add event listeners for debugging
                            toggle.addEventListener('show.bs.dropdown', function() {
                                console.log(`📂 Profil Dropdown ${index + 1} is opening`);
                            });

                            toggle.addEventListener('shown.bs.dropdown', function() {
                                console.log(`📂 Profil Dropdown ${index + 1} is now open`);
                            });

                            toggle.addEventListener('click', function(e) {
                                console.log(`🖱️ Profil Dropdown ${index + 1} clicked`, e);
                            });

                        } catch (error) {
                            console.error(`❌ Error initializing profil dropdown ${index + 1}:`, error);
                        }
                    });

                } else {
                    console.error('❌ Bootstrap is not loaded on profil page!');
                }
            }, 500); // Attendre 500ms pour que Bootstrap soit chargé
        });

        // Validation du formulaire
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        function changerMotDePasse() {
            TechShop.showNotification('Fonctionnalité de changement de mot de passe en cours de développement', 'info');
        }

        function telechargerDonnees() {
            TechShop.showNotification('Téléchargement des données en cours de développement', 'info');
        }

        function supprimerCompte() {
            if (confirm('Êtes-vous sûr de vouloir supprimer définitivement votre compte ? Cette action est irréversible.')) {
                TechShop.showNotification('Fonctionnalité de suppression de compte en cours de développement', 'info');
            }
        }
    </script>

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

</body>
</html>
