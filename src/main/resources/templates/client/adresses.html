<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Mes Adresses - TechShop</title>
</head>
<body class="page-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <!-- Main Content Wrapper -->
    <div class="content-wrapper">
        <div class="container mt-4">
            <div class="row">
                <!-- Sidebar Client -->
                <div th:replace="~{fragments/layout :: client-sidebar}"></div>

                <!-- Main Content -->
                <div class="col-md-9">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-map-marker-alt"></i> Mes Adresses</h2>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterAdresseModal">
                            <i class="fas fa-plus"></i> Ajouter une adresse
                        </button>
                    </div>

                    <!-- Messages d'alerte -->
                    <div th:if="${success}" class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle"></i>
                        <span th:text="${success}">Succès</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <div th:if="${error}" class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span th:text="${error}">Erreur</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <!-- Liste des adresses -->
                    <div th:if="${#lists.isEmpty(adresses)}" class="text-center py-5">
                        <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">Aucune adresse</h4>
                        <p class="text-muted">Vous n'avez pas encore ajouté d'adresse.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterAdresseModal">
                            <i class="fas fa-plus"></i> Ajouter ma première adresse
                        </button>
                    </div>

                    <div th:if="${!#lists.isEmpty(adresses)}" class="row">
                        <div th:each="adresse : ${adresses}" class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-home"></i> Adresse
                                    </h6>
                                    <p class="card-text">
                                        <span th:text="${adresse.adresseComplete}">123 Rue de la Paix</span><br>
                                        <span th:text="${adresse.codePostal}">75001</span> <span th:text="${adresse.ville}">Paris</span>
                                    </p>
                                    <small class="text-muted">
                                        Ajoutée le <span th:text="${#temporals.format(adresse.creeLe, 'dd/MM/yyyy')}">01/01/2024</span>
                                    </small>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                data-bs-toggle="modal" 
                                                th:data-bs-target="'#modifierAdresseModal' + ${adresse.id}">
                                            <i class="fas fa-edit"></i> Modifier
                                        </button>
                                        <form th:action="@{/client/adresses/{id}/supprimer(id=${adresse.id})}" method="post" 
                                              onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette adresse ?')">
                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                <i class="fas fa-trash"></i> Supprimer
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Ajouter Adresse -->
    <div class="modal fade" id="ajouterAdresseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form th:action="@{/client/adresses}" method="post" th:object="${nouvelleAdresse}">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-plus"></i> Ajouter une adresse</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="adresseComplete" class="form-label">Adresse complète *</label>
                            <textarea class="form-control" id="adresseComplete" th:field="*{adresseComplete}" 
                                      rows="3" placeholder="Numéro, rue, bâtiment, etc." required></textarea>
                            <div th:if="${#fields.hasErrors('adresseComplete')}" class="text-danger">
                                <small th:errors="*{adresseComplete}">Erreur</small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <label for="codePostal" class="form-label">Code postal *</label>
                                <input type="text" class="form-control" id="codePostal" th:field="*{codePostal}" 
                                       placeholder="75001" required>
                                <div th:if="${#fields.hasErrors('codePostal')}" class="text-danger">
                                    <small th:errors="*{codePostal}">Erreur</small>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <label for="ville" class="form-label">Ville *</label>
                                <input type="text" class="form-control" id="ville" th:field="*{ville}" 
                                       placeholder="Paris" required>
                                <div th:if="${#fields.hasErrors('ville')}" class="text-danger">
                                    <small th:errors="*{ville}">Erreur</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

</body>
</html>
