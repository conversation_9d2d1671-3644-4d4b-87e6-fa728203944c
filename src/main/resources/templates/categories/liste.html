<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Catégories - TechShop</title>
</head>
<body>
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tags"></i> Nos Catégories</h2>
                    <span class="badge bg-primary fs-6" th:text="${#lists.size(categories)} + ' catégorie(s)'">0 catégorie(s)</span>
                </div>
            </div>
        </div>

        <!-- Message d'erreur -->
        <div th:if="${param.error}" class="alert alert-warning alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle"></i>
            <span th:switch="${param.error[0]}">
                <span th:case="'categorie-non-trouvee'">La catégorie demandée n'existe pas.</span>
                <span th:case="*">Une erreur s'est produite.</span>
            </span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Grille des catégories -->
        <div class="row" th:if="${categories != null and !#lists.isEmpty(categories)}">
            <div class="col-md-6 col-lg-4 mb-4" th:each="categorie : ${categories}">
                <div class="card h-100 category-card">
                    <div class="card-img-top bg-primary text-white d-flex align-items-center justify-content-center"
                         style="height: 200px;">
                        <div class="text-center">
                            <i class="fas fa-tag fa-3x mb-3"></i>
                            <h4 th:text="${categorie.nomCategorie}">Nom de la catégorie</h4>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title" th:text="${categorie.nomCategorie}">Nom de la catégorie</h5>

                        <p class="card-text text-muted" th:if="${categorie.typeCategorie}">
                            Type : <span th:text="${categorie.typeCategorie}">Type</span>
                        </p>

                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-box"></i>
                                    <span th:text="${categorie.produits != null ? #lists.size(categorie.produits) : 0}">0</span> produit(s)
                                </small>
                                <a th:href="@{/categories/{id}(id=${categorie.id})}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye"></i> Voir produits
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message si aucune catégorie -->
        <div th:if="${categories == null or #lists.isEmpty(categories)}" class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">Aucune catégorie disponible</h4>
            <p class="text-muted">Les catégories seront bientôt disponibles.</p>
        </div>

        <!-- Section catégories populaires -->
        <div th:if="${categories != null and !#lists.isEmpty(categories)}" class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4"><i class="fas fa-fire"></i> Catégories Populaires</h3>
                <div class="row">
                    <div class="col-md-3 mb-3" th:each="categorie, iterStat : ${categories}" th:if="${iterStat.index < 4}">
                        <a th:href="@{/categories/{id}(id=${categorie.id})}" class="text-decoration-none">
                            <div class="card bg-light border-0 h-100 popular-category">
                                <div class="card-body text-center">
                                    <i class="fas fa-tag fa-2x text-primary mb-2"></i>
                                    <h6 class="card-title" th:text="${categorie.nomCategorie}">Catégorie</h6>
                                    <small class="text-muted" th:text="${categorie.produits != null ? #lists.size(categorie.produits) : 0} + ' produits'">0 produits</small>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lien vers tous les produits -->
        <div class="text-center mt-5">
            <a th:href="@{/produits}" class="btn btn-primary btn-lg">
                <i class="fas fa-shopping-bag"></i> Voir tous les produits
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <style>
        .category-card {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .popular-category {
            transition: transform 0.3s;
        }
        .popular-category:hover {
            transform: translateY(-3px);
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/techshop.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

    <script>
        // Initialisation du compteur de panier pour la page categories
        function initialiserCompteurPanierCategories() {
            // Récupérer la valeur du compteur depuis l'attribut data ou le contenu existant
            const compteurs = document.querySelectorAll('.cart-counter');

            compteurs.forEach(element => {
                // Récupérer la valeur initiale depuis l'attribut Thymeleaf ou le contenu
                const valeurInitiale = element.textContent.trim();
                const compteur = parseInt(valeurInitiale) || 0;

                console.log('🚀 Initialisation immédiate compteur panier (categories):', compteur);

                // Afficher immédiatement le compteur sans attendre d'appel AJAX
                element.textContent = compteur;

                // Toujours afficher le compteur pour les utilisateurs anonymes
                element.style.display = '';
                element.classList.remove('d-none');
            });
        }

        // Initialisation immédiate du compteur (sans attendre DOMContentLoaded)
        function initCompteurPanierImmediat() {
            // Vérifier si les éléments sont déjà disponibles
            const compteurs = document.querySelectorAll('.cart-counter');
            if (compteurs.length > 0) {
                // Utiliser d'abord l'initialisation immédiate avec les données serveur
                initialiserCompteurPanierCategories();
            } else {
                // Si pas encore disponibles, réessayer dans 50ms
                setTimeout(initCompteurPanierImmediat, 50);
            }
        }

        // Initialisation immédiate du compteur (sans attendre DOMContentLoaded)
        initCompteurPanierImmediat();

        // Debugging Bootstrap Dropdowns pour la page categories liste
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS (PAGE CATEGORIES LISTE) ===');

            // Initialiser le compteur de panier
            initCompteurPanierImmediat();

            // Attendre que Bootstrap soit chargé
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap is loaded on categories liste page, version:', bootstrap.Tooltip.VERSION || 'unknown');

                    // Find all dropdown toggles
                    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                    console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles on categories liste page');

                    // Initialize dropdowns manually
                    dropdownToggles.forEach((toggle, index) => {
                        console.log(`🎯 Categories Liste Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                        try {
                            // Create Bootstrap dropdown instance
                            const dropdown = new bootstrap.Dropdown(toggle);
                            console.log(`✅ Categories Liste Dropdown ${index + 1} initialized successfully`);

                            // Add event listeners for debugging
                            toggle.addEventListener('show.bs.dropdown', function() {
                                console.log(`📂 Categories Liste Dropdown ${index + 1} is opening`);
                            });

                            toggle.addEventListener('shown.bs.dropdown', function() {
                                console.log(`📂 Categories Liste Dropdown ${index + 1} is now open`);
                            });

                            toggle.addEventListener('click', function(e) {
                                console.log(`🖱️ Categories Liste Dropdown ${index + 1} clicked`, e);
                            });

                        } catch (error) {
                            console.error(`❌ Error initializing categories liste dropdown ${index + 1}:`, error);
                        }
                    });

                } else {
                    console.error('❌ Bootstrap is not loaded on categories liste page!');
                }
            }, 500); // Attendre 500ms pour que Bootstrap soit chargé
        });
    </script>

</body>
</html>
