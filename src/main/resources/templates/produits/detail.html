<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title th:text="${produit.designation} + ' - TechShop'">Produit - TechShop</title>
</head>
<body class="page-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <!-- Main Content Wrapper -->
    <div class="content-wrapper">
        <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Accueil</a></li>
                <li class="breadcrumb-item"><a th:href="@{/produits}">Produits</a></li>
                <li class="breadcrumb-item active" th:text="${produit.designation}">Produit</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Image du produit -->
            <div class="col-md-6">
                <div class="card">
                    <img th:src="${produit.urlImage} ?: 'https://via.placeholder.com/500x400'"
                         class="card-img-top" alt="Image produit" style="height: 400px; object-fit: cover;">
                </div>

                <!-- Images miniatures (placeholder) -->
                <div class="row mt-3">
                    <div class="col-3" th:each="i : ${#numbers.sequence(1, 4)}">
                        <img th:src="${produit.urlImage} ?: 'https://via.placeholder.com/100x80'"
                             class="img-thumbnail" alt="Miniature" style="height: 80px; object-fit: cover; cursor: pointer;">
                    </div>
                </div>
            </div>

            <!-- Détails du produit -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h1 class="card-title" th:text="${produit.designation}">Nom du Produit</h1>

                        <div class="mb-3">
                            <span class="badge bg-info me-2" th:text="${produit.categorie?.nomCategorie}">Catégorie</span>
                            <span class="badge bg-secondary" th:text="${produit.marque}">Marque</span>
                        </div>

                        <div class="mb-3">
                            <span class="text-muted">Référence: </span>
                            <strong th:text="${produit.reference}">REF001</strong>
                        </div>

                        <div class="mb-4">
                            <h2 class="text-primary" th:text="${#numbers.formatDecimal(produit.prix, 0, 2)} + ' €'">Prix</h2>
                        </div>

                        <!-- Stock -->
                        <div class="mb-4">
                            <div th:if="${produit.qteStock > 0}" class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>En stock</strong> - <span th:text="${produit.qteStock}">10</span> disponible(s)
                            </div>
                            <div th:if="${produit.qteStock == 0}" class="alert alert-danger">
                                <i class="fas fa-times-circle"></i>
                                <strong>Rupture de stock</strong>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div sec:authorize="hasRole('CLIENT')" class="mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="quantite" class="form-label">Quantité</label>
                                    <div class="input-group">
                                        <button class="btn btn-outline-secondary" type="button" onclick="decrementQuantity()">-</button>
                                        <input type="number" class="form-control text-center" id="quantite" value="1"
                                               min="1" th:max="${produit.qteStock}">
                                        <button class="btn btn-outline-secondary" type="button" onclick="incrementQuantity()">+</button>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" th:disabled="${produit.qteStock == 0}"
                                                th:onclick="|ajouterAuPanier(${produit.id}, document.getElementById('quantite').value)|">
                                            <i class="fas fa-cart-plus"></i> Ajouter au panier
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <button class="btn btn-outline-danger w-100" th:onclick="|toggleFavori(${produit.id})|">
                                        <i class="far fa-heart"></i> Ajouter aux favoris
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-info w-100">
                                        <i class="fas fa-share-alt"></i> Partager
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Message pour utilisateurs non connectés -->
                        <div sec:authorize="!isAuthenticated()" class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <a th:href="@{/login}">Connectez-vous</a> pour ajouter ce produit à votre panier.
                        </div>

                        <!-- Informations supplémentaires -->
                        <div class="mt-4">
                            <h5>Informations produit</h5>
                            <ul class="list-unstyled">
                                <li><strong>Marque:</strong> <span th:text="${produit.marque}">Marque</span></li>
                                <li><strong>Référence:</strong> <span th:text="${produit.reference}">REF001</span></li>
                                <li><strong>Catégorie:</strong> <span th:text="${produit.categorie?.nomCategorie}">Catégorie</span></li>
                                <li><strong>Disponibilité:</strong>
                                    <span th:if="${produit.qteStock > 0}" class="text-success">En stock</span>
                                    <span th:if="${produit.qteStock == 0}" class="text-danger">Rupture</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Description détaillée -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#description">Description</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#specifications">Spécifications</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#reviews">Avis clients</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <div class="tab-pane fade show active" id="description">
                                <h5>Description du produit</h5>
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
                                <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                            </div>
                            <div class="tab-pane fade" id="specifications">
                                <h5>Spécifications techniques</h5>
                                <table class="table table-striped">
                                    <tr><td><strong>Marque</strong></td><td th:text="${produit.marque}">Marque</td></tr>
                                    <tr><td><strong>Référence</strong></td><td th:text="${produit.reference}">REF001</td></tr>
                                    <tr><td><strong>Catégorie</strong></td><td th:text="${produit.categorie?.nomCategorie}">Catégorie</td></tr>
                                    <tr><td><strong>Poids</strong></td><td>À définir</td></tr>
                                    <tr><td><strong>Dimensions</strong></td><td>À définir</td></tr>
                                </table>
                            </div>
                            <div class="tab-pane fade" id="reviews">
                                <h5>Avis clients</h5>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-star fa-2x mb-3"></i>
                                    <p>Aucun avis pour le moment.</p>
                                    <p>Soyez le premier à donner votre avis sur ce produit !</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Produits similaires -->
        <div class="row mt-4" th:if="${produitsSimilaires != null and !#lists.isEmpty(produitsSimilaires)}">
            <div class="col-12">
                <h3><i class="fas fa-lightbulb"></i> Produits similaires</h3>
                <div class="row">
                    <div class="col-md-3 mb-3" th:each="produitSimilaire : ${produitsSimilaires}">
                        <div class="card h-100">
                            <img th:src="${produitSimilaire.urlImage} ?: 'https://via.placeholder.com/200x150'"
                                 class="card-img-top" alt="Produit similaire" style="height: 150px; object-fit: cover;">
                            <div class="card-body">
                                <h6 class="card-title" th:text="${produitSimilaire.designation}">Produit</h6>
                                <p class="card-text">
                                    <span class="text-primary fw-bold" th:text="${#numbers.formatDecimal(produitSimilaire.prix, 0, 2)} + ' €'">Prix</span>
                                </p>
                            </div>
                            <div class="card-footer">
                                <a th:href="@{/produits/{id}(id=${produitSimilaire.id})}" class="btn btn-outline-primary btn-sm w-100">
                                    Voir le produit
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div> <!-- End container -->
    </div> <!-- End content-wrapper -->

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/techshop.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

    <script>
        // Debugging Bootstrap Dropdowns pour la page produit detail
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS (PAGE PRODUIT DETAIL) ===');

            // Attendre que Bootstrap soit chargé
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap is loaded on produit detail page, version:', bootstrap.Tooltip.VERSION || 'unknown');

                    // Find all dropdown toggles
                    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                    console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles on produit detail page');

                    // Initialize dropdowns manually
                    dropdownToggles.forEach((toggle, index) => {
                        console.log(`🎯 Produit Detail Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                        try {
                            // Create Bootstrap dropdown instance
                            const dropdown = new bootstrap.Dropdown(toggle);
                            console.log(`✅ Produit Detail Dropdown ${index + 1} initialized successfully`);

                            // Add event listeners for debugging
                            toggle.addEventListener('show.bs.dropdown', function() {
                                console.log(`📂 Produit Detail Dropdown ${index + 1} is opening`);
                            });

                            toggle.addEventListener('shown.bs.dropdown', function() {
                                console.log(`📂 Produit Detail Dropdown ${index + 1} is now open`);
                            });

                            toggle.addEventListener('click', function(e) {
                                console.log(`🖱️ Produit Detail Dropdown ${index + 1} clicked`, e);
                            });

                        } catch (error) {
                            console.error(`❌ Error initializing produit detail dropdown ${index + 1}:`, error);
                        }
                    });

                } else {
                    console.error('❌ Bootstrap is not loaded on produit detail page!');
                }
            }, 500); // Attendre 500ms pour que Bootstrap soit chargé
        });

        function incrementQuantity() {
            const input = document.getElementById('quantite');
            const max = parseInt(input.getAttribute('max'));
            const current = parseInt(input.value);
            if (current < max) {
                input.value = current + 1;
            }
        }

        function decrementQuantity() {
            const input = document.getElementById('quantite');
            const current = parseInt(input.value);
            if (current > 1) {
                input.value = current - 1;
            }
        }
    </script>
</body>
</html>
