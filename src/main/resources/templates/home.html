<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head}">
    <title>Accueil - TechShop</title>
    <style>
        /* Styles pour les boutons de la page d'accueil */
        .card-footer {
            padding: 0.75rem;
            background-color: #f8f9fa;
            border-top: 1px solid rgba(0,0,0,.125);
        }

        .card-footer .d-flex {
            min-height: 32px; /* Hauteur minimale pour alignement */
        }

        /* Espacement entre les boutons panier et favoris */
        .card-footer .me-2 {
            margin-right: 0.5rem !important;
        }

        /* Animation pour les boutons favoris */
        .ajouter-favoris-btn:hover,
        .ajouter-favoris-anonyme-btn:hover {
            transform: scale(1.05);
            transition: all 0.2s ease;
        }

        /* Animation pour les boutons panier */
        .ajouter-panier-btn:hover,
        .ajouter-panier-anonyme-btn:hover {
            transform: scale(1.05);
            transition: all 0.2s ease;
        }

        /* Style pour les boutons en cours de traitement */
        .btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        /* Disposition des boutons : Gauche (Voir) - Centre (Panier) - Droite (Favoris) */
        .card-footer .d-flex.justify-content-between {
            gap: 0.5rem;
            position: relative;
        }

        /* Assurer que le bouton panier est parfaitement centré */
        .card-footer .d-flex.justify-content-between > .btn-primary {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        /* Assurer que le bouton admin (gestion) est aussi centré */
        .card-footer .d-flex.justify-content-between > .btn-outline-secondary {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        /* Style pour les badges de stock */
        .badge {
            font-size: 0.75em;
        }
    </style>
</head>
<body class="page-wrapper">
    <!-- Navigation -->
    <nav th:replace="~{fragments/layout :: navbar}"></nav>

    <!-- Main Content Wrapper -->
    <div class="content-wrapper">
        <!-- Hero Section -->
        <div class="hero-section bg-primary text-white py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <h1 class="display-4 fw-bold">Bienvenue chez TechShop</h1>
                    <p class="lead">Découvrez notre large gamme de produits technologiques de qualité</p>
                    <a th:href="@{/produits}" class="btn btn-light btn-lg">
                        <i class="fas fa-shopping-bag"></i> Voir nos produits
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Section -->
    <div class="container my-5">
        <h2 class="text-center mb-4">Nos Catégories</h2>
        <div class="row" th:if="${categories}">
            <div class="col-md-4 mb-4" th:each="categorie : ${categories}">
                <div class="card h-100 text-center">
                    <div class="card-body d-flex flex-column align-items-center">
                        <div class="mb-3">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 180px; height: 180px; border: 4px solid #007bff;
                                        box-shadow: 0 6px 12px rgba(0,0,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;
                                        background-color: #f8f9fa; overflow: hidden;"
                                 onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 16px rgba(0,0,0,0.25)'"
                                 onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 6px 12px rgba(0,0,0,0.15)'">
                                <img th:src="${categorie.urlImage} ?: 'https://via.placeholder.com/300x200'"
                                     class="rounded-circle category-image" alt="Catégorie"
                                     style="width: 160px; height: 160px; object-fit: cover;">
                            </div>
                        </div>
                        <h5 class="card-title" th:text="${categorie.nomCategorie}">Nom Catégorie</h5>
                        <span class="badge bg-secondary mb-3" th:text="${categorie.typeCategorie}">Type</span>
                        <div class="mt-auto">
                            <a th:href="@{/categories/{id}(id=${categorie.id})}" class="btn btn-primary" title="Voir les produits">
                                <i class="fas fa-eye"></i> Produits
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Products -->
    <div class="container my-5">
        <h2 class="text-center mb-4">Produits en Vedette</h2>
        <div class="row" th:if="${produits}">
            <div class="col-md-3 mb-4" th:each="produit : ${#lists.size(produits) > 8 ? produits.subList(0, 8) : produits}">
                <div class="card h-100">
                    <img th:src="${produit.urlImage} ?: 'https://via.placeholder.com/250x200'"
                         class="card-img-top" alt="Produit" style="height: 200px; object-fit: cover;">
                    <div class="card-body">
                        <h6 class="card-title" th:text="${produit.designation}">Nom Produit</h6>
                        <p class="card-text">
                            <small class="text-muted" th:text="${produit.marque}">Marque</small><br>
                            <span class="fw-bold text-primary" th:text="${#numbers.formatDecimal(produit.prix, 0, 2)} + ' €'">Prix</span>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-success" th:if="${produit.qteStock > 0}">En stock</span>
                            <span class="badge bg-danger" th:if="${produit.qteStock == 0}">Rupture</span>
                            <small class="text-muted" th:text="'Stock: ' + ${produit.qteStock}">Stock</small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <!-- GAUCHE: Bouton Voir détails -->
                            <a th:href="@{/produits/{id}(id=${produit.id})}"
                               class="btn btn-outline-primary btn-sm"
                               title="Voir les détails">
                                <i class="fas fa-eye"></i>
                            </a>

                            <!-- CENTRE: Bouton Ajouter au panier -->
                            <!-- Pour utilisateurs connectés (CLIENT) -->
                            <button class="btn btn-primary btn-sm ajouter-panier-btn"
                                    sec:authorize="hasRole('CLIENT')"
                                    th:disabled="${produit.qteStock == 0}"
                                    th:data-produit-id="${produit.id}"
                                    th:data-produit-nom="${produit.designation}"
                                    th:data-produit-prix="${produit.prix}"
                                    title="Ajouter au panier">
                                <i class="fas fa-cart-plus"></i>
                            </button>

                            <!-- Pour utilisateurs anonymes -->
                            <button class="btn btn-primary btn-sm ajouter-panier-anonyme-btn"
                                    sec:authorize="!isAuthenticated()"
                                    th:disabled="${produit.qteStock == 0}"
                                    th:data-produit-id="${produit.id}"
                                    th:data-produit-nom="${produit.designation}"
                                    th:data-produit-prix="${produit.prix}"
                                    title="Ajouter au panier">
                                <i class="fas fa-cart-plus"></i>
                            </button>

                            <!-- Pour admins (bouton de gestion au centre) -->
                            <a th:href="@{/admin/produits/{id}(id=${produit.id})}"
                               class="btn btn-outline-secondary btn-sm"
                               sec:authorize="hasRole('ADMIN')"
                               title="Gérer ce produit">
                                <i class="fas fa-cog"></i>
                            </a>

                            <!-- DROITE: Bouton Ajouter aux favoris -->
                            <!-- Pour utilisateurs connectés (CLIENT) -->
                            <button class="btn btn-outline-danger btn-sm ajouter-favoris-btn"
                                    sec:authorize="hasRole('CLIENT')"
                                    th:data-produit-id="${produit.id}"
                                    th:data-produit-nom="${produit.designation}"
                                    title="Ajouter aux favoris">
                                <i class="fas fa-heart"></i>
                            </button>

                            <!-- Pour utilisateurs anonymes -->
                            <button class="btn btn-outline-danger btn-sm ajouter-favoris-anonyme-btn"
                                    sec:authorize="!isAuthenticated()"
                                    th:data-produit-id="${produit.id}"
                                    th:data-produit-nom="${produit.designation}"
                                    title="Ajouter aux favoris">
                                <i class="fas fa-heart"></i>
                            </button>

                            <!-- Pour admins (pas de bouton favoris, espace vide) -->
                            <div sec:authorize="hasRole('ADMIN')" style="width: 32px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a th:href="@{/produits}" class="btn btn-outline-primary btn-lg">
                Voir tous les produits <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
    </div> <!-- End content-wrapper -->

    <!-- Footer -->
    <footer th:replace="~{fragments/layout :: footer}"></footer>

    <script>
        // Debugging Bootstrap Dropdowns pour la page d'accueil
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DEBUGGING BOOTSTRAP DROPDOWNS (PAGE ACCUEIL) ===');

            // Attendre que Bootstrap soit chargé
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap is loaded on accueil page, version:', bootstrap.Tooltip.VERSION || 'unknown');

                    // Find all dropdown toggles
                    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                    console.log('🔍 Found', dropdownToggles.length, 'dropdown toggles on accueil page');

                    // Initialize dropdowns manually
                    dropdownToggles.forEach((toggle, index) => {
                        console.log(`🎯 Accueil Dropdown ${index + 1}:`, toggle.id || 'no-id', toggle);

                        try {
                            // Create Bootstrap dropdown instance
                            const dropdown = new bootstrap.Dropdown(toggle);
                            console.log(`✅ Accueil Dropdown ${index + 1} initialized successfully`);

                            // Add event listeners for debugging
                            toggle.addEventListener('show.bs.dropdown', function() {
                                console.log(`📂 Accueil Dropdown ${index + 1} is opening`);
                            });

                            toggle.addEventListener('shown.bs.dropdown', function() {
                                console.log(`📂 Accueil Dropdown ${index + 1} is now open`);
                            });

                            toggle.addEventListener('click', function(e) {
                                console.log(`🖱️ Accueil Dropdown ${index + 1} clicked`, e);
                            });

                        } catch (error) {
                            console.error(`❌ Error initializing accueil dropdown ${index + 1}:`, error);
                        }
                    });

                } else {
                    console.error('❌ Bootstrap is not loaded on accueil page!');
                }
            }, 500); // Attendre 500ms pour que Bootstrap soit chargé

            // ==================== LOGIQUE PANIER ET FAVORIS ====================

            // Gestion séparée et directe des boutons d'ajout au panier

            // Boutons pour utilisateurs connectés (CLIENT)
            const boutonsAjouterPanierClient = document.querySelectorAll('.ajouter-panier-btn');
            console.log('🛒 Found', boutonsAjouterPanierClient.length, 'boutons ajouter au panier (clients connectés)');

            boutonsAjouterPanierClient.forEach(bouton => {
                bouton.addEventListener('click', function(e) {
                    e.preventDefault();

                    console.log('🎯 Bouton panier CLIENT cliqué:', {
                        classe: this.className,
                        endpoint: '/client/panier/ajouter'
                    });

                    ajouterAuPanier(this, '/client/panier/ajouter');
                });
            });

            // Boutons pour utilisateurs anonymes
            const boutonsAjouterPanierAnonyme = document.querySelectorAll('.ajouter-panier-anonyme-btn');
            console.log('🛒 Found', boutonsAjouterPanierAnonyme.length, 'boutons ajouter au panier (anonymes)');

            boutonsAjouterPanierAnonyme.forEach(bouton => {
                bouton.addEventListener('click', function(e) {
                    e.preventDefault();

                    console.log('🎯 Bouton panier ANONYME cliqué:', {
                        classe: this.className,
                        endpoint: '/panier/ajouter'
                    });

                    ajouterAuPanier(this, '/panier/ajouter');
                });
            });

            // Gestion séparée et directe des boutons d'ajout aux favoris

            // Boutons pour utilisateurs connectés (CLIENT)
            const boutonsAjouterFavorisClient = document.querySelectorAll('.ajouter-favoris-btn');
            console.log('💖 Found', boutonsAjouterFavorisClient.length, 'boutons ajouter aux favoris (clients connectés)');

            boutonsAjouterFavorisClient.forEach(bouton => {
                bouton.addEventListener('click', function(e) {
                    e.preventDefault();

                    const produitId = this.getAttribute('data-produit-id');
                    const endpoint = `/client/favoris/ajouter/${produitId}`;

                    console.log('🎯 Bouton favoris CLIENT cliqué:', {
                        classe: this.className,
                        produitId: produitId,
                        endpoint: endpoint
                    });

                    ajouterAuxFavoris(this, endpoint);
                });
            });

            // Boutons pour utilisateurs anonymes
            const boutonsAjouterFavorisAnonyme = document.querySelectorAll('.ajouter-favoris-anonyme-btn');
            console.log('💖 Found', boutonsAjouterFavorisAnonyme.length, 'boutons ajouter aux favoris (anonymes)');

            boutonsAjouterFavorisAnonyme.forEach(bouton => {
                bouton.addEventListener('click', function(e) {
                    e.preventDefault();

                    const produitId = this.getAttribute('data-produit-id');
                    const endpoint = `/favoris/ajouter/${produitId}`;

                    console.log('🎯 Bouton favoris ANONYME cliqué:', {
                        classe: this.className,
                        produitId: produitId,
                        endpoint: endpoint
                    });

                    ajouterAuxFavoris(this, endpoint);
                });
            });
        });

        // ==================== FONCTIONS UTILITAIRES ====================

        // Fonction pour restaurer l'état original d'un bouton
        function restaurerBouton(bouton, contenuOriginal, classesOriginales) {
            bouton.innerHTML = contenuOriginal;
            bouton.className = classesOriginales.join(' ');
            bouton.disabled = false;
        }

        // Fonction pour ajouter un produit au panier
        function ajouterAuPanier(bouton, endpoint) {
            // Validation des données
            const produitId = bouton.getAttribute('data-produit-id');
            const produitNom = bouton.getAttribute('data-produit-nom');
            const produitPrix = bouton.getAttribute('data-produit-prix');

            if (!produitId) {
                console.error('❌ ID du produit manquant');
                afficherToast('Erreur : ID du produit manquant', 'error');
                return;
            }

            // Déterminer le type d'utilisateur pour le debug
            const isAnonymous = endpoint === '/panier/ajouter';
            const userType = isAnonymous ? 'ANONYME' : 'CONNECTÉ';

            console.log('🛒 Ajout au panier:', {
                produitId,
                produitNom,
                produitPrix,
                endpoint,
                userType,
                isAnonymous,
                boutonClasses: bouton.className
            });

            // Éviter les clics multiples
            if (bouton.disabled) {
                console.log('⚠️ Bouton déjà en cours de traitement');
                return;
            }

            // Désactiver le bouton temporairement
            const boutonOriginal = bouton.innerHTML;
            const classesOriginales = Array.from(bouton.classList);
            bouton.disabled = true;
            bouton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Récupérer le token CSRF
            const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');

            // Préparer les headers avec CSRF
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            };
            if (csrfToken && csrfHeader) {
                headers[csrfHeader] = csrfToken;
            }

            // Vérification de sécurité : s'assurer que l'endpoint est correct
            if (endpoint !== '/panier/ajouter' && endpoint !== '/client/panier/ajouter') {
                console.error('❌ Endpoint invalide:', endpoint);
                afficherToast('Erreur : endpoint invalide', 'error');
                restaurerBouton(bouton, boutonOriginal, classesOriginales);
                return;
            }

            // Appel AJAX pour ajouter au panier
            fetch(endpoint, {
                method: 'POST',
                headers: headers,
                body: `produitId=${produitId}&quantite=1`
            })
            .then(response => {
                console.log('📡 Réponse serveur panier:', response.status, response.statusText);

                if (response.ok) {
                    return response.text();
                } else if (response.status === 404) {
                    throw new Error('Produit non trouvé');
                } else if (response.status === 403) {
                    throw new Error('Accès refusé');
                } else if (response.status === 500) {
                    throw new Error('Erreur interne du serveur');
                } else {
                    // Pour les utilisateurs anonymes, ne pas traiter 401 comme une erreur
                    // car l'endpoint /panier/ajouter est accessible aux anonymes
                    throw new Error(`Erreur serveur (${response.status})`);
                }
            })
            .then(data => {
                console.log('✅ Réponse ajout panier:', data);

                if (data === 'success' || data.includes('success')) {
                    // Afficher un message de succès
                    bouton.innerHTML = '<i class="fas fa-check"></i>';
                    bouton.className = 'btn btn-success btn-sm';

                    // Mettre à jour le compteur du panier
                    mettreAJourCompteurPanier();

                    // Afficher une notification toast
                    afficherToast(`${produitNom || 'Produit'} ajouté au panier !`, 'success');

                    // Remettre le bouton normal après 2 secondes
                    setTimeout(() => {
                        restaurerBouton(bouton, boutonOriginal, classesOriginales);
                    }, 2000);
                } else {
                    // Gérer les erreurs métier
                    console.warn('⚠️ Erreur métier panier:', data);
                    restaurerBouton(bouton, boutonOriginal, classesOriginales);
                    afficherToast(data || 'Erreur lors de l\'ajout au panier', 'warning');
                }
            })
            .catch(error => {
                console.error('❌ Erreur ajout panier:', error);
                restaurerBouton(bouton, boutonOriginal, classesOriginales);
                afficherToast(error.message || 'Erreur lors de l\'ajout au panier', 'error');
            });
        }

        // Fonction pour ajouter un produit aux favoris
        function ajouterAuxFavoris(bouton, endpoint) {
            // Validation des données
            const produitId = bouton.getAttribute('data-produit-id');
            const produitNom = bouton.getAttribute('data-produit-nom');

            if (!produitId) {
                console.error('❌ ID du produit manquant pour favoris');
                afficherToast('Erreur : ID du produit manquant', 'error');
                return;
            }

            // Déterminer le type d'utilisateur pour le debug
            const isAnonymousFavoris = endpoint.startsWith('/favoris/ajouter/');
            const userTypeFavoris = isAnonymousFavoris ? 'ANONYME' : 'CONNECTÉ';

            console.log('💖 Ajout aux favoris:', {
                produitId,
                produitNom,
                endpoint,
                userType: userTypeFavoris,
                isAnonymous: isAnonymousFavoris,
                boutonClasses: bouton.className
            });

            // Éviter les clics multiples
            if (bouton.disabled) {
                console.log('⚠️ Bouton favoris déjà en cours de traitement');
                return;
            }

            // Désactiver le bouton temporairement
            const boutonOriginal = bouton.innerHTML;
            const classesOriginales = Array.from(bouton.classList);
            bouton.disabled = true;
            bouton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Récupérer le token CSRF
            const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');

            // Préparer les headers avec CSRF
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            };
            if (csrfToken && csrfHeader) {
                headers[csrfHeader] = csrfToken;
            }

            // Vérification de sécurité : s'assurer que l'endpoint est correct
            if (!endpoint.startsWith('/favoris/ajouter/') && !endpoint.startsWith('/client/favoris/ajouter/')) {
                console.error('❌ Endpoint favoris invalide:', endpoint);
                afficherToast('Erreur : endpoint favoris invalide', 'error');
                restaurerBouton(bouton, boutonOriginal, classesOriginales);
                return;
            }

            // Appel AJAX pour ajouter aux favoris (pas de body car l'ID est dans l'URL)
            fetch(endpoint, {
                method: 'POST',
                headers: headers
            })
            .then(response => {
                console.log('📡 Réponse serveur favoris:', response.status, response.statusText);

                if (response.ok) {
                    return response.text();
                } else if (response.status === 404) {
                    throw new Error('Produit non trouvé');
                } else if (response.status === 403) {
                    throw new Error('Accès refusé');
                } else if (response.status === 500) {
                    throw new Error('Erreur interne du serveur');
                } else {
                    // Pour les utilisateurs anonymes, ne pas traiter 401 comme une erreur
                    // car l'endpoint /favoris/ajouter est accessible aux anonymes
                    throw new Error(`Erreur serveur (${response.status})`);
                }
            })
            .then(data => {
                console.log('✅ Réponse ajout favoris:', data);

                if (data === 'success' || data.includes('success')) {
                    // Afficher un message de succès
                    bouton.innerHTML = '<i class="fas fa-heart"></i>';
                    bouton.className = 'btn btn-danger btn-sm';

                    // Mettre à jour le compteur des favoris
                    mettreAJourCompteurFavoris();

                    // Afficher une notification toast
                    afficherToast(`${produitNom || 'Produit'} ajouté aux favoris !`, 'success');

                    // Remettre le bouton normal après 2 secondes
                    setTimeout(() => {
                        restaurerBouton(bouton, boutonOriginal, classesOriginales);
                    }, 2000);
                } else if (data.includes('déjà') || data.includes('already')) {
                    // Produit déjà en favoris
                    console.warn('⚠️ Produit déjà en favoris');
                    restaurerBouton(bouton, boutonOriginal, classesOriginales);
                    afficherToast('Ce produit est déjà dans vos favoris', 'info');
                } else {
                    // Autres erreurs métier
                    console.warn('⚠️ Erreur métier favoris:', data);
                    restaurerBouton(bouton, boutonOriginal, classesOriginales);
                    afficherToast(data || 'Erreur lors de l\'ajout aux favoris', 'warning');
                }
            })
            .catch(error => {
                console.error('❌ Erreur ajout favoris:', error);
                restaurerBouton(bouton, boutonOriginal, classesOriginales);
                afficherToast(error.message || 'Erreur lors de l\'ajout aux favoris', 'error');
            });
        }

        // Fonction pour initialiser immédiatement le compteur du panier avec les données serveur
        function initialiserCompteurPanierImmediat() {
            // Récupérer la valeur du compteur depuis l'attribut data ou le contenu existant
            const compteurs = document.querySelectorAll('.cart-counter');

            compteurs.forEach(element => {
                // Récupérer la valeur initiale depuis l'attribut Thymeleaf ou le contenu
                const valeurInitiale = element.textContent.trim();
                const compteur = parseInt(valeurInitiale) || 0;

                console.log('🚀 Initialisation immédiate compteur panier (home):', compteur);

                // Afficher immédiatement le compteur sans attendre d'appel AJAX
                element.textContent = compteur;

                // Toujours afficher le compteur pour les utilisateurs anonymes
                element.style.display = '';
                element.classList.remove('d-none');
            });
        }

        // Fonction pour mettre à jour le compteur du panier via AJAX
        function mettreAJourCompteurPanier() {
            fetch('/panier/compteur')
                .then(response => response.json())
                .then(compteur => {
                    console.log('🔄 Mise à jour compteur panier:', compteur);

                    // Mettre à jour tous les compteurs de panier
                    const compteurs = document.querySelectorAll('.cart-counter');
                    compteurs.forEach(element => {
                        element.textContent = compteur;
                        // Toujours afficher le compteur pour les utilisateurs anonymes
                        element.style.display = '';
                        element.classList.remove('d-none');

                        // Ajouter une animation quand le compteur change
                        element.classList.add('updated');
                        setTimeout(() => {
                            element.classList.remove('updated');
                        }, 600);
                    });
                })
                .catch(error => {
                    console.error('❌ Erreur mise à jour compteur panier:', error);
                });
        }

        // Fonction pour mettre à jour le compteur des favoris via AJAX
        function mettreAJourCompteurFavoris() {
            fetch('/favoris/compteur')
                .then(response => response.json())
                .then(compteur => {
                    console.log('🔄 Mise à jour compteur favoris:', compteur);

                    // Mettre à jour tous les compteurs de favoris
                    const compteurs = document.querySelectorAll('.favorites-counter');
                    compteurs.forEach(element => {
                        element.textContent = compteur;
                        // Toujours afficher le compteur
                        element.style.display = '';
                        element.classList.remove('d-none');

                        // Ajouter une animation quand le compteur change
                        element.classList.add('updated');
                        setTimeout(() => {
                            element.classList.remove('updated');
                        }, 600);
                    });
                })
                .catch(error => {
                    console.error('❌ Erreur mise à jour compteur favoris:', error);
                });
        }

        // Initialisation immédiate du compteur (sans attendre DOMContentLoaded)
        function initCompteurPanierImmediat() {
            // Vérifier si les éléments sont déjà disponibles
            const compteurs = document.querySelectorAll('.cart-counter');
            if (compteurs.length > 0) {
                // Utiliser d'abord l'initialisation immédiate avec les données serveur
                initialiserCompteurPanierImmediat();
            } else {
                // Si pas encore disponibles, réessayer dans 50ms
                setTimeout(initCompteurPanierImmediat, 50);
            }
        }

        // Fonction pour afficher des notifications toast
        function afficherToast(message, type = 'info') {
            // Mapper les types aux classes Bootstrap et icônes
            const typeConfig = {
                'success': { class: 'alert-success', icon: 'check-circle', duration: 3000 },
                'error': { class: 'alert-danger', icon: 'exclamation-circle', duration: 4000 },
                'warning': { class: 'alert-warning', icon: 'exclamation-triangle', duration: 3500 },
                'info': { class: 'alert-info', icon: 'info-circle', duration: 3000 }
            };

            const config = typeConfig[type] || typeConfig['info'];

            // Créer un élément toast
            const toast = document.createElement('div');
            toast.className = `alert ${config.class} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
            toast.innerHTML = `
                <i class="fas fa-${config.icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Ajouter au body
            document.body.appendChild(toast);

            // Supprimer automatiquement après la durée configurée
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 150); // Attendre la fin de l'animation fade
                }
            }, config.duration);
        }

        // Initialiser au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page home chargée');
            // Initialiser immédiatement le compteur avec les données serveur
            initCompteurPanierImmediat();
        });

        // Initialisation immédiate du compteur (sans attendre DOMContentLoaded)
        initCompteurPanierImmediat();
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/script.js}"></script>
    <script th:src="@{/js/favoris.js}"></script>
    <script th:src="@{/js/techshop.js}"></script>
    <script th:src="@{/js/panier.js}"></script>

</body>
</html>
