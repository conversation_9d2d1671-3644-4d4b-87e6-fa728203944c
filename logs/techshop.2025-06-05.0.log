2025-06-05 00:00:00.018 [scheduling-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.statut='EN_COURS' 
        and p1_0.cree_le<?
2025-06-05 00:02:58.774 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing POST /client/panier/ajouter
2025-06-05 00:02:58.776 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:02:58.778 [http-nio-8080-exec-9] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:02:58.783 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:02:58.784 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:02:58.785 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:02:58.789 [http-nio-8080-exec-8] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:02:58.824 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:02:58.824 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:02:58.825 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:02:58.825 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:02:58.826 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:02:58.826 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:03:06.430 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /
2025-06-05 00:03:06.431 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:06.432 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /
2025-06-05 00:03:06.438 [http-nio-8080-exec-10] DEBUG [c2ed7d63] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.qte_stock>?
2025-06-05 00:03:06.455 [http-nio-8080-exec-10] DEBUG [c2ed7d63] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0 
    where
        c1_0.parent_categ_id is null
2025-06-05 00:03:06.459 [http-nio-8080-exec-10] DEBUG [c2ed7d63] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:03:06.462 [http-nio-8080-exec-10] DEBUG [c2ed7d63] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:03:06.465 [http-nio-8080-exec-10] DEBUG [c2ed7d63] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:03:06.467 [http-nio-8080-exec-10] DEBUG [c2ed7d63] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:03:06.469 [http-nio-8080-exec-10] DEBUG [c2ed7d63] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:03:06.469 [http-nio-8080-exec-10] DEBUG [c2ed7d63] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:03:06.472 [http-nio-8080-exec-10] DEBUG [c2ed7d63] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:03:06.548 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:03:06.548 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:03:06.549 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:06.549 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:06.551 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:03:06.553 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:03:06.825 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-05 00:03:06.826 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:06.829 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-05 00:03:06.829 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-05 00:03:06.830 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:06.832 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-05 00:03:06.834 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-05 00:03:06.835 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:06.837 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-05 00:03:06.836 [http-nio-8080-exec-5] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-05 00:03:07.006 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-05 00:03:07.007 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:07.012 [http-nio-8080-exec-9] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:03:07.078 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:03:07.079 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:07.079 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:03:07.080 [http-nio-8080-exec-8] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:03:10.877 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing POST /client/panier/ajouter
2025-06-05 00:03:10.878 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:10.880 [http-nio-8080-exec-4] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:03:10.890 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:03:10.891 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:10.892 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:03:10.893 [http-nio-8080-exec-1] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:03:10.920 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:03:10.920 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:03:10.921 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:10.921 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:03:10.922 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:03:10.922 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:08:55.214 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /
2025-06-05 00:08:55.216 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:08:55.216 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /
2025-06-05 00:08:55.222 [http-nio-8080-exec-6] DEBUG [b9d52a49] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.qte_stock>?
2025-06-05 00:08:55.234 [http-nio-8080-exec-6] DEBUG [b9d52a49] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0 
    where
        c1_0.parent_categ_id is null
2025-06-05 00:08:55.239 [http-nio-8080-exec-6] DEBUG [b9d52a49] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:08:55.243 [http-nio-8080-exec-6] DEBUG [b9d52a49] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:08:55.246 [http-nio-8080-exec-6] DEBUG [b9d52a49] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:08:55.248 [http-nio-8080-exec-6] DEBUG [b9d52a49] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:08:55.249 [http-nio-8080-exec-6] DEBUG [b9d52a49] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:08:55.250 [http-nio-8080-exec-6] DEBUG [b9d52a49] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:08:55.251 [http-nio-8080-exec-6] DEBUG [b9d52a49] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:08:55.280 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:08:55.280 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:08:55.281 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:08:55.281 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:08:55.282 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:08:55.282 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:08:55.528 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-05 00:08:55.528 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-05 00:08:55.528 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-05 00:08:55.529 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:08:55.529 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:08:55.529 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:08:55.530 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-05 00:08:55.530 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-05 00:08:55.530 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-05 00:08:55.532 [http-nio-8080-exec-4] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-05 00:08:55.762 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-05 00:08:55.763 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:08:55.766 [http-nio-8080-exec-3] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:08:55.781 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:08:55.782 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:08:55.783 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:08:55.784 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:24:49.474 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /
2025-06-05 00:24:49.475 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:49.476 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /
2025-06-05 00:24:49.479 [http-nio-8080-exec-6] DEBUG [44b9f653] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.qte_stock>?
2025-06-05 00:24:49.485 [http-nio-8080-exec-6] DEBUG [44b9f653] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0 
    where
        c1_0.parent_categ_id is null
2025-06-05 00:24:49.487 [http-nio-8080-exec-6] DEBUG [44b9f653] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:24:49.489 [http-nio-8080-exec-6] DEBUG [44b9f653] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:24:49.490 [http-nio-8080-exec-6] DEBUG [44b9f653] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:24:49.491 [http-nio-8080-exec-6] DEBUG [44b9f653] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:24:49.491 [http-nio-8080-exec-6] DEBUG [44b9f653] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:24:49.492 [http-nio-8080-exec-6] DEBUG [44b9f653] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-05 00:24:49.493 [http-nio-8080-exec-6] DEBUG [44b9f653] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:24:49.544 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:24:49.545 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:49.547 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:24:49.547 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:24:49.548 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:49.549 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:24:49.581 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-05 00:24:49.581 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-05 00:24:49.582 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:49.582 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:49.582 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-05 00:24:49.583 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-05 00:24:49.583 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-05 00:24:49.583 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:49.584 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-05 00:24:49.586 [http-nio-8080-exec-1] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-05 00:24:49.658 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-05 00:24:49.659 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:49.663 [http-nio-8080-exec-3] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:24:49.716 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:24:49.717 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:49.717 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:24:49.719 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:24:53.050 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing POST /client/panier/ajouter
2025-06-05 00:24:53.051 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:53.053 [http-nio-8080-exec-7] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:24:53.059 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:24:53.059 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:53.060 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:24:53.061 [http-nio-8080-exec-2] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:24:53.082 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:24:53.082 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:24:53.083 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:53.083 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:24:53.084 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:24:53.084 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:39:05.586 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing POST /client/panier/ajouter
2025-06-05 00:39:05.587 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:05.588 [http-nio-8080-exec-4] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:39:05.593 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:39:05.594 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:05.594 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:39:05.595 [http-nio-8080-exec-1] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:39:05.616 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:39:05.616 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:39:05.617 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:05.617 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:05.619 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:39:05.619 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:39:13.431 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing POST /client/panier/ajouter
2025-06-05 00:39:13.431 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:13.432 [http-nio-8080-exec-7] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:39:13.436 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:39:13.437 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:13.438 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:39:13.439 [http-nio-8080-exec-2] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:39:13.457 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:39:13.457 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:39:13.458 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:13.458 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:13.459 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:39:13.459 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:39:16.728 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing POST /client/panier/ajouter
2025-06-05 00:39:16.729 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:16.730 [http-nio-8080-exec-5] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:39:16.738 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:39:16.739 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:16.740 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:39:16.740 [http-nio-8080-exec-8] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:39:16.760 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:39:16.760 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:39:16.760 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:16.760 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:16.761 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:39:16.761 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:39:20.248 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing POST /client/panier/ajouter
2025-06-05 00:39:20.249 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:20.250 [http-nio-8080-exec-10] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:39:20.252 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:39:20.252 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:20.252 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:39:20.252 [http-nio-8080-exec-3] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:39:20.271 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:39:20.271 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:39:20.272 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:20.272 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:39:20.272 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:39:20.272 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:42:23.289 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing POST /client/panier/ajouter
2025-06-05 00:42:23.291 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:42:23.292 [http-nio-8080-exec-5] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-05 00:42:23.296 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-05 00:42:23.297 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:42:23.297 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-05 00:42:23.298 [http-nio-8080-exec-8] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 1 favoris
2025-06-05 00:42:23.317 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-05 00:42:23.317 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-05 00:42:23.317 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:42:23.317 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-05 00:42:23.318 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-05 00:42:23.318 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-05 00:57:20.108 [SpringApplicationShutdownHook] INFO  [] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:57:20.112 [SpringApplicationShutdownHook] INFO  [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-05 00:57:20.116 [SpringApplicationShutdownHook] INFO  [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
