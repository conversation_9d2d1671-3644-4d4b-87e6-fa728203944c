2025-06-21 19:42:41.899 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /produits/recherche
2025-06-21 19:42:41.910 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:41.913 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /produits/recherche
2025-06-21 19:42:41.923 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0
2025-06-21 19:42:41.933 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:42:41.937 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:42:41.941 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:42:41.943 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:42:41.945 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:42:41.950 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:42:41.973 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:42:41.980 [http-nio-8080-exec-10] DEBUG [de81d5b1] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:42:41.982 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:41.986 [http-nio-8080-exec-10] DEBUG [de81d5b1] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:41.988 [http-nio-8080-exec-10] DEBUG [de81d5b1] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:42:42.028 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:42:42.029 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.030 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:42:42.031 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:42:42.031 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.031 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:42:42.032 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.033 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:42:42.033 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:42:42.033 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:42:42.034 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.034 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:42:42.035 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:42:42.035 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.036 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:42:42.039 [http-nio-8080-exec-1] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:42:42.084 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:42:42.084 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:42:42.085 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.085 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.087 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:42:42.087 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:42:42.190 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:42:42.191 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.197 [http-nio-8080-exec-9] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:42:42.202 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:42:42.203 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:42.204 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:42:42.205 [http-nio-8080-exec-4] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:42:42.207 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:42.211 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:42.212 [http-nio-8080-exec-4] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:42:43.139 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:42:43.139 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:43.140 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:42:43.142 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:43.143 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:43.144 [http-nio-8080-exec-10] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
2025-06-21 19:42:50.588 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:42:50.589 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:50.589 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:42:50.590 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:50.592 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:50.593 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:50.594 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:50.595 [http-nio-8080-exec-3] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:42:53.351 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:42:53.352 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:53.353 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:42:53.356 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:53.360 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:53.362 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:53.363 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:53.364 [http-nio-8080-exec-7] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:42:53.368 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:42:53.369 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:53.370 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:42:53.374 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:53.376 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:53.377 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:53.378 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:53.379 [http-nio-8080-exec-8] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:42:54.987 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:42:54.987 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:54.988 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:42:54.989 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:54.990 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:54.991 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:54.992 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:54.992 [http-nio-8080-exec-6] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:42:56.947 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:42:56.949 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:56.949 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:42:56.950 [http-nio-8080-exec-1] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:42:56.952 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:56.955 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:56.956 [http-nio-8080-exec-1] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:42:56.998 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:42:56.999 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:42:57.000 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:57.000 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:57.000 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:42:57.000 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:57.001 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:42:57.001 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:42:57.001 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:42:57.123 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:42:57.124 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:57.129 [http-nio-8080-exec-4] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:42:57.134 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:42:57.135 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:57.136 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:42:57.137 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:42:57.140 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:57.143 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:57.144 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:42:58.347 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:42:58.349 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:58.356 [http-nio-8080-exec-3] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:42:58.363 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:42:58.364 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:58.365 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:42:58.366 [http-nio-8080-exec-7] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:42:58.368 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:58.372 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:58.373 [http-nio-8080-exec-7] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:42:58.557 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:42:58.558 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:58.559 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:42:58.561 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:58.564 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:58.564 [http-nio-8080-exec-8] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
2025-06-21 19:42:58.898 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:42:58.899 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:42:58.899 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:42:58.900 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:42:58.901 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:42:58.901 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:58.902 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:42:58.902 [http-nio-8080-exec-6] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:00.593 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /categories
2025-06-21 19:43:00.594 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.595 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /categories
2025-06-21 19:43:00.598 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:00.599 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:00.600 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:00.602 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:00.603 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:00.604 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:00.606 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:00.610 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:00.616 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:00.618 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:00.630 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:00.632 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:00.639 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:00.641 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:00.646 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:00.648 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:00.655 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:00.656 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:00.661 [http-nio-8080-exec-1] DEBUG [95f81c70] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:00.663 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:00.665 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:00.668 [http-nio-8080-exec-1] DEBUG [95f81c70] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:00.669 [http-nio-8080-exec-1] DEBUG [95f81c70] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:00.706 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:00.707 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:00.707 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:00.708 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:00.708 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.708 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.708 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.708 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.709 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:00.709 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:00.710 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:00.710 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:43:00.710 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:00.710 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.710 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:00.711 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:43:00.712 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.713 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:00.713 [http-nio-8080-exec-3] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:43:00.727 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:00.728 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.729 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:00.826 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:00.828 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.832 [http-nio-8080-exec-8] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:00.835 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:00.836 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:00.837 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:00.837 [http-nio-8080-exec-6] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:00.839 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:00.841 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:00.841 [http-nio-8080-exec-6] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:09.423 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /categories
2025-06-21 19:43:09.424 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.425 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /categories
2025-06-21 19:43:09.430 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:09.432 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:09.433 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:09.434 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:09.435 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:09.436 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:09.438 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:09.443 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:09.451 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:09.453 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:09.466 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:09.468 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:09.475 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:09.476 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:09.485 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:09.487 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:09.493 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:09.495 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:09.498 [http-nio-8080-exec-1] DEBUG [7969fe69] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:09.500 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:09.502 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:09.507 [http-nio-8080-exec-1] DEBUG [7969fe69] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:09.508 [http-nio-8080-exec-1] DEBUG [7969fe69] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:09.548 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:09.550 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.550 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:09.551 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.553 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:09.554 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:09.555 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:09.555 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.558 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:09.555 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:09.555 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.555 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:09.562 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:09.556 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:43:09.564 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.567 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:09.568 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.562 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.571 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:43:09.571 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:09.571 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:09.575 [http-nio-8080-exec-10] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:43:09.700 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:09.701 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.706 [http-nio-8080-exec-8] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:09.710 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:09.710 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:09.710 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:09.711 [http-nio-8080-exec-6] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:09.713 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:09.716 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:09.717 [http-nio-8080-exec-6] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:10.758 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /produits
2025-06-21 19:43:10.760 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.760 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /produits
2025-06-21 19:43:10.766 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:10.768 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:10.770 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:10.772 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:10.773 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:10.775 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:10.776 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:10.779 [http-nio-8080-exec-1] DEBUG [f3634f38] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:10.782 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:10.787 [http-nio-8080-exec-1] DEBUG [f3634f38] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:10.790 [http-nio-8080-exec-1] DEBUG [f3634f38] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:10.832 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:10.832 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:10.832 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:10.833 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:10.833 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.833 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.833 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.833 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.834 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:10.831 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:10.835 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.835 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:10.835 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:10.835 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:10.836 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:10.877 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:10.877 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.878 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:10.975 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:10.976 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.980 [http-nio-8080-exec-10] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:10.986 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:10.987 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:10.987 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:10.988 [http-nio-8080-exec-8] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:10.989 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:10.993 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:10.994 [http-nio-8080-exec-8] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:11.844 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:11.845 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:11.845 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:11.848 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:11.850 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:11.852 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:11.853 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:11.854 [http-nio-8080-exec-6] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:11.856 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:11.856 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:11.857 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:11.861 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:11.864 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:11.865 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:11.866 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:11.866 [http-nio-8080-exec-1] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:15.634 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:15.634 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:15.635 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:15.637 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:15.639 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:15.640 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:15.641 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:15.641 [http-nio-8080-exec-9] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:15.643 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:15.643 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:15.644 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:15.645 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:15.648 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:15.650 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:15.651 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:15.652 [http-nio-8080-exec-4] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:18.929 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /
2025-06-21 19:43:18.930 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:18.930 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /
2025-06-21 19:43:18.932 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.qte_stock>?
2025-06-21 19:43:18.938 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0 
    where
        c1_0.parent_categ_id is null
2025-06-21 19:43:18.941 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:18.942 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:18.943 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:18.943 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:18.944 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:18.945 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:18.946 [http-nio-8080-exec-2] DEBUG [2a3b431d] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:18.948 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:18.951 [http-nio-8080-exec-2] DEBUG [2a3b431d] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:18.952 [http-nio-8080-exec-2] DEBUG [2a3b431d] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:18.992 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:18.993 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:18.993 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:18.993 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:18.993 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:18.993 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:18.994 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:18.994 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:18.994 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:18.994 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:18.995 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:18.995 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:19.029 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:19.029 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:19.030 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:19.030 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:19.031 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:19.031 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:19.105 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:19.106 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:19.113 [http-nio-8080-exec-1] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:19.118 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:19.119 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:19.119 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:19.120 [http-nio-8080-exec-9] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:19.121 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:19.125 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:19.127 [http-nio-8080-exec-9] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:23.984 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /categories
2025-06-21 19:43:23.985 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:23.985 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /categories
2025-06-21 19:43:23.988 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:23.990 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:23.991 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:23.993 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:23.994 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:23.995 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:23.996 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:24.000 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:24.006 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:24.008 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:24.020 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:24.021 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:24.028 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:24.030 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:24.037 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:24.040 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:24.048 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:24.050 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.categorie_id=?
2025-06-21 19:43:24.053 [http-nio-8080-exec-4] DEBUG [415fbf13] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:24.055 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:24.056 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    update
        categories 
    set
        modifie_le=?,
        nom_categorie=?,
        parent_categ_id=?,
        type_categorie=?,
        url_image=? 
    where
        id=?
2025-06-21 19:43:24.062 [http-nio-8080-exec-4] DEBUG [415fbf13] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:24.063 [http-nio-8080-exec-4] DEBUG [415fbf13] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:24.103 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:24.105 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.105 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:24.105 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.106 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:24.106 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:24.106 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:24.107 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.107 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.107 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:43:24.108 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:24.108 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.108 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:24.108 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:24.106 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:24.109 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:43:24.109 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.110 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:24.111 [http-nio-8080-exec-8] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:43:24.131 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:24.132 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.133 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:24.243 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:24.245 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.250 [http-nio-8080-exec-1] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:24.255 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:24.256 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:24.256 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:24.257 [http-nio-8080-exec-9] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:24.259 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:24.263 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:24.264 [http-nio-8080-exec-9] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:27.715 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /produits/recherche
2025-06-21 19:43:27.716 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.716 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /produits/recherche
2025-06-21 19:43:27.720 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0
2025-06-21 19:43:27.725 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:27.729 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:27.730 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:27.731 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:27.732 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:27.732 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:27.733 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:27.735 [http-nio-8080-exec-4] DEBUG [bf771af9] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:27.737 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:27.742 [http-nio-8080-exec-4] DEBUG [bf771af9] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:27.743 [http-nio-8080-exec-4] DEBUG [bf771af9] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:27.784 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:27.784 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:27.784 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:27.785 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.785 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.785 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.786 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:27.787 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:27.788 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:27.827 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:27.827 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:43:27.828 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:27.828 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:27.828 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.828 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.828 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.828 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.829 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:27.829 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:27.829 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:27.830 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:43:27.831 [http-nio-8080-exec-5] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:43:27.925 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:27.926 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.929 [http-nio-8080-exec-1] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:27.935 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:27.936 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:27.937 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:27.938 [http-nio-8080-exec-9] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:27.939 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:27.941 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:27.942 [http-nio-8080-exec-9] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:28.883 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:43:28.883 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:28.884 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:43:28.886 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:28.887 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:28.888 [http-nio-8080-exec-4] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
2025-06-21 19:43:29.163 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:29.164 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:29.164 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:29.166 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:29.167 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:29.169 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:29.171 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:29.172 [http-nio-8080-exec-2] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:29.174 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:29.175 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:29.175 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:29.177 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:29.179 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:29.182 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:29.184 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:29.184 [http-nio-8080-exec-10] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:30.332 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:30.332 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:30.333 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:30.334 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:30.336 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:30.338 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:30.338 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:30.339 [http-nio-8080-exec-3] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:31.459 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /produits/recherche
2025-06-21 19:43:31.460 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.461 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /produits/recherche
2025-06-21 19:43:31.465 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0
2025-06-21 19:43:31.471 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:31.474 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:31.477 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:31.478 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:31.479 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:31.479 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:31.480 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:31.482 [http-nio-8080-exec-8] DEBUG [6b73a1a0] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:31.484 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:31.486 [http-nio-8080-exec-8] DEBUG [6b73a1a0] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:31.486 [http-nio-8080-exec-8] DEBUG [6b73a1a0] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:31.529 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:31.529 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:31.529 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:31.530 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.530 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.530 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.531 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:31.532 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:43:31.532 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.532 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:31.532 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:31.533 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:31.533 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.533 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:31.534 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:43:31.537 [http-nio-8080-exec-9] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:43:31.582 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:31.582 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:31.583 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.583 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.584 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:31.584 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:31.688 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:31.690 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.694 [http-nio-8080-exec-10] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:31.698 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:31.699 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:31.699 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:31.699 [http-nio-8080-exec-3] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:31.701 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:31.705 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:31.706 [http-nio-8080-exec-3] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:32.639 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:43:32.640 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:32.641 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:43:32.643 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:32.644 [http-nio-8080-exec-8] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:32.644 [http-nio-8080-exec-8] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
2025-06-21 19:43:32.855 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:32.855 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:32.856 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:32.859 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:32.863 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:32.864 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:32.865 [http-nio-8080-exec-7] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:32.865 [http-nio-8080-exec-7] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:32.869 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:32.869 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:32.871 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:32.875 [http-nio-8080-exec-5] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:32.877 [http-nio-8080-exec-5] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:32.879 [http-nio-8080-exec-5] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:32.880 [http-nio-8080-exec-5] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:32.881 [http-nio-8080-exec-5] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:37.460 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:37.461 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:37.461 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:37.463 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:37.465 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:37.465 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:37.466 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:37.466 [http-nio-8080-exec-1] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:39.403 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /
2025-06-21 19:43:39.404 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.405 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /
2025-06-21 19:43:39.408 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.qte_stock>?
2025-06-21 19:43:39.413 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0 
    where
        c1_0.parent_categ_id is null
2025-06-21 19:43:39.416 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:39.418 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:39.419 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:39.420 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:39.420 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:39.421 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:39.422 [http-nio-8080-exec-6] DEBUG [0b2e7052] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:39.424 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:39.428 [http-nio-8080-exec-6] DEBUG [0b2e7052] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:39.429 [http-nio-8080-exec-6] DEBUG [0b2e7052] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:39.465 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:39.465 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:39.465 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:39.466 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:39.466 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.466 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.466 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.468 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:39.468 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:39.468 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:39.466 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.472 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:39.521 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:39.521 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:39.521 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.521 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.522 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:39.522 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:39.665 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:39.666 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.671 [http-nio-8080-exec-7] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:39.675 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:39.676 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:39.676 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:39.677 [http-nio-8080-exec-5] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:39.678 [http-nio-8080-exec-5] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:39.682 [http-nio-8080-exec-5] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:39.683 [http-nio-8080-exec-5] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:42.388 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /produits/recherche
2025-06-21 19:43:42.390 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.391 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /produits/recherche
2025-06-21 19:43:42.396 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0
2025-06-21 19:43:42.403 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:42.406 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:42.408 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:42.410 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:42.411 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:42.412 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:42.413 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:42.417 [http-nio-8080-exec-1] DEBUG [13f34a89] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:42.419 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:42.422 [http-nio-8080-exec-1] DEBUG [13f34a89] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:42.423 [http-nio-8080-exec-1] DEBUG [13f34a89] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:42.471 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:42.473 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:42.474 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.473 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:42.475 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.475 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.476 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:42.476 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:42.477 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:42.477 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:42.478 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:43:42.478 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.479 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.480 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:42.480 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:43:42.483 [http-nio-8080-exec-2] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:43:42.531 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:42.531 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:42.532 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.532 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.533 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:42.533 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:42.608 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:42.609 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.612 [http-nio-8080-exec-7] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:42.619 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:42.620 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:42.620 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:42.621 [http-nio-8080-exec-5] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:42.623 [http-nio-8080-exec-5] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:42.627 [http-nio-8080-exec-5] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:42.628 [http-nio-8080-exec-5] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:43.587 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:43:43.588 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:43.590 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:43:43.599 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:43.609 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:43.611 [http-nio-8080-exec-1] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
2025-06-21 19:43:45.307 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:45.308 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:45.309 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:45.310 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:45.311 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:45.313 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:45.314 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:45.314 [http-nio-8080-exec-10] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:47.136 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /produits/recherche
2025-06-21 19:43:47.137 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.137 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /produits/recherche
2025-06-21 19:43:47.141 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0
2025-06-21 19:43:47.145 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:47.148 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:47.149 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:47.149 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:47.150 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:47.150 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:47.150 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:47.151 [http-nio-8080-exec-4] DEBUG [5956f64f] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:47.153 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:47.154 [http-nio-8080-exec-4] DEBUG [5956f64f] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:47.155 [http-nio-8080-exec-4] DEBUG [5956f64f] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:47.185 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:47.185 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:47.185 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:47.185 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:47.186 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:43:47.186 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.186 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.186 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.186 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.186 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.187 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:47.187 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:47.187 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:47.187 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:43:47.188 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:47.190 [http-nio-8080-exec-8] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:43:47.232 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:47.232 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:47.233 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.233 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.234 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:47.234 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:47.311 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:47.313 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.316 [http-nio-8080-exec-1] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:47.320 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:47.321 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:47.321 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:47.322 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:47.323 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:47.326 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:47.327 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:48.263 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:43:48.264 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:48.265 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:43:48.266 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:48.269 [http-nio-8080-exec-4] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:48.271 [http-nio-8080-exec-4] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
2025-06-21 19:43:49.963 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:49.964 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:49.965 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:49.966 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:49.968 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:49.970 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:49.971 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:49.972 [http-nio-8080-exec-3] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:49.974 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:49.974 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:49.975 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:49.977 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:49.979 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:49.981 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:49.983 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:49.984 [http-nio-8080-exec-9] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:43:51.180 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /
2025-06-21 19:43:51.181 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.181 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /
2025-06-21 19:43:51.185 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.qte_stock>?
2025-06-21 19:43:51.190 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0 
    where
        c1_0.parent_categ_id is null
2025-06-21 19:43:51.193 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:51.195 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:51.196 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:51.196 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:51.197 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:51.198 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:51.199 [http-nio-8080-exec-2] DEBUG [3b8f4318] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:51.200 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:51.203 [http-nio-8080-exec-2] DEBUG [3b8f4318] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:51.205 [http-nio-8080-exec-2] DEBUG [3b8f4318] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:51.241 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:51.242 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.244 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:51.246 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:51.247 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:51.246 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:51.246 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:51.248 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.248 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.248 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.248 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.249 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:51.249 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:51.250 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:51.251 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:51.297 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:51.298 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.298 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:51.365 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:51.367 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.371 [http-nio-8080-exec-4] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:51.374 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:51.375 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:51.375 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:51.375 [http-nio-8080-exec-3] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:51.377 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:51.380 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:51.381 [http-nio-8080-exec-3] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:53.788 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /produits/recherche
2025-06-21 19:43:53.788 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.789 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /produits/recherche
2025-06-21 19:43:53.794 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0
2025-06-21 19:43:53.799 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        c1_0.id,
        c1_0.cree_le,
        c1_0.modifie_le,
        c1_0.nom_categorie,
        c1_0.parent_categ_id,
        c1_0.type_categorie,
        c1_0.url_image 
    from
        categories c1_0
2025-06-21 19:43:53.801 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:53.804 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:53.806 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:53.806 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:53.807 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:53.807 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        sc1_0.parent_categ_id,
        sc1_0.id,
        sc1_0.cree_le,
        sc1_0.modifie_le,
        sc1_0.nom_categorie,
        sc1_0.type_categorie,
        sc1_0.url_image 
    from
        categories sc1_0 
    where
        sc1_0.parent_categ_id=?
2025-06-21 19:43:53.809 [http-nio-8080-exec-9] DEBUG [c7a98462] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:53.810 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:53.812 [http-nio-8080-exec-9] DEBUG [c7a98462] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:53.813 [http-nio-8080-exec-9] DEBUG [c7a98462] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:53.850 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:43:53.850 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:53.851 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:43:53.851 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/techshop.js
2025-06-21 19:43:53.851 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/script.js
2025-06-21 19:43:53.851 [http-nio-8080-exec-8] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.851 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.851 [http-nio-8080-exec-7] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.851 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.851 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.852 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:43:53.853 [http-nio-8080-exec-8] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/techshop.js
2025-06-21 19:43:53.853 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:43:53.853 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:43:53.853 [http-nio-8080-exec-7] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/script.js
2025-06-21 19:43:53.855 [http-nio-8080-exec-8] ERROR [] c.t.e.e.GlobalExceptionHandler - Erreur inattendue: No static resource js/techshop.js. - URL: http://localhost:8080/js/techshop.js
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource js/techshop.js.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:151)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:129)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-21 19:43:53.902 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/favoris.js
2025-06-21 19:43:53.902 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /js/panier.js
2025-06-21 19:43:53.903 [http-nio-8080-exec-5] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.903 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.904 [http-nio-8080-exec-5] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/favoris.js
2025-06-21 19:43:53.904 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /js/panier.js
2025-06-21 19:43:53.986 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:43:53.987 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.992 [http-nio-8080-exec-4] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:43:53.995 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:43:53.996 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:53.996 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:43:53.997 [http-nio-8080-exec-3] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:43:53.998 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:54.001 [http-nio-8080-exec-3] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:54.002 [http-nio-8080-exec-3] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:43:54.943 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:43:54.943 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:54.943 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:43:54.945 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:54.947 [http-nio-8080-exec-9] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:54.948 [http-nio-8080-exec-9] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
2025-06-21 19:43:58.993 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:43:58.994 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:43:58.995 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:43:58.998 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:43:59.000 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:43:59.003 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:59.005 [http-nio-8080-exec-1] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:43:59.006 [http-nio-8080-exec-1] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:44:00.451 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/contenu
2025-06-21 19:44:00.451 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:44:00.452 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/contenu
2025-06-21 19:44:00.453 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:44:00.454 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        lp1_0.panier_id,
        lp1_0.produit_id,
        lp1_0.cout_ligne,
        lp1_0.cree_le,
        lp1_0.modifie_le,
        lp1_0.quantite 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:44:00.454 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:44:00.455 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.categorie_id,
        p1_0.cree_le,
        p1_0.designation,
        p1_0.marque,
        p1_0.modifie_le,
        p1_0.prix,
        p1_0.qte_stock,
        p1_0.reference,
        p1_0.url_image 
    from
        produits p1_0 
    where
        p1_0.id=?
2025-06-21 19:44:00.455 [http-nio-8080-exec-2] DEBUG [] c.t.e.c.PanierPublicController - API contenu panier public: 2 articles
2025-06-21 19:45:50.973 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:45:50.974 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:45:50.974 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:45:50.975 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:45:50.977 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:45:50.982 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:45:50.984 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:45:51.052 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:45:51.057 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:45:51.058 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:45:51.059 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:45:51.060 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:45:51.061 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:45:51.062 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:45:51.062 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:45:51.065 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:45:51.200 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:45:51.203 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:45:51.210 [http-nio-8080-exec-1] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:45:51.223 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:45:51.224 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:45:51.225 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:45:51.226 [http-nio-8080-exec-2] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:45:51.227 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:45:51.230 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:45:51.232 [http-nio-8080-exec-2] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:45:52.620 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:45:52.620 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:45:52.621 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:45:52.623 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:45:52.624 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:45:52.625 [http-nio-8080-exec-6] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
2025-06-21 19:48:19.461 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /register
2025-06-21 19:48:19.462 [http-nio-8080-exec-10] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:48:19.463 [http-nio-8080-exec-10] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /register
2025-06-21 19:48:19.464 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:48:19.467 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:48:19.470 [http-nio-8080-exec-10] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:48:19.471 [http-nio-8080-exec-10] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:48:19.526 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-unified.css
2025-06-21 19:48:19.526 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/style.css
2025-06-21 19:48:19.526 [http-nio-8080-exec-9] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:48:19.526 [http-nio-8080-exec-4] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:48:19.527 [http-nio-8080-exec-9] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/style.css
2025-06-21 19:48:19.527 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /css/navbar-responsive-gaps.css
2025-06-21 19:48:19.528 [http-nio-8080-exec-3] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:48:19.529 [http-nio-8080-exec-3] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-responsive-gaps.css
2025-06-21 19:48:19.527 [http-nio-8080-exec-4] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /css/navbar-unified.css
2025-06-21 19:48:19.626 [http-nio-8080-exec-1] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-06-21 19:48:19.627 [http-nio-8080-exec-1] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:48:19.633 [http-nio-8080-exec-1] DEBUG [] o.s.s.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-21 19:48:19.637 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /login
2025-06-21 19:48:19.637 [http-nio-8080-exec-2] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:48:19.637 [http-nio-8080-exec-2] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /login
2025-06-21 19:48:19.638 [http-nio-8080-exec-2] DEBUG [] c.t.e.interceptor.FavorisInterceptor - Compteur favoris anonyme: 3 favoris
2025-06-21 19:48:19.639 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:48:19.645 [http-nio-8080-exec-2] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:48:19.647 [http-nio-8080-exec-2] DEBUG [] c.t.e.interceptor.PanierInterceptor - Compteur panier anonyme ajouté au modèle: 2 articles
2025-06-21 19:48:21.061 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Securing GET /panier/compteur
2025-06-21 19:48:21.061 [http-nio-8080-exec-6] DEBUG [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 19:48:21.061 [http-nio-8080-exec-6] DEBUG [] o.s.security.web.FilterChainProxy - Secured GET /panier/compteur
2025-06-21 19:48:21.063 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        p1_0.id,
        p1_0.cout_panier,
        p1_0.cree_le,
        p1_0.modifie_le,
        p1_0.session_id,
        p1_0.statut,
        p1_0.utilisateur_id 
    from
        paniers p1_0 
    where
        p1_0.session_id=? 
        and p1_0.statut='EN_COURS'
2025-06-21 19:48:21.064 [http-nio-8080-exec-6] DEBUG [] org.hibernate.SQL - 
    select
        coalesce(sum(lp1_0.quantite), 0) 
    from
        lignes_panier lp1_0 
    where
        lp1_0.panier_id=?
2025-06-21 19:48:21.064 [http-nio-8080-exec-6] DEBUG [] c.t.e.c.PanierPublicController - API compteur panier public: 2 articles
